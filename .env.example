# Hotmart Configuration
HOTMART_PRODUCT_ID=
HOTMART_PRIVATE_KEY=
HOTMART_ACCESS_TOKEN=
HOTMART_WEBHOOK_SECRET=
HOTMART_WEBHOOK_URL=https://tu-dominio.com/api/webhooks/hotmart

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Database Schema
# Users Table
# - id: uuid (primary key)
# - email: string
# - name: string
# - hotmart_id: string
# - level: integer (1-3)
# - balance: decimal
# - created_at: timestamp

# Affiliate Links Table
# - id: uuid (primary key)
# - user_id: uuid (foreign key)
# - app_id: integer
# - url: string
# - created_at: timestamp

# Sales Table
# - id: uuid (primary key)
# - hotmart_transaction_id: string
# - affiliate_id: uuid (foreign key)
# - app_id: integer
# - amount: decimal
# - commission: decimal
# - created_at: timestamp
# - ip_address: string