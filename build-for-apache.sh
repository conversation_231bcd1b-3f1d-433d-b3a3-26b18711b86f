#!/bin/bash

# Limpiar carpetas de compilación anteriores
echo "Limpiando carpetas de compilación anteriores..."
rm -rf .next build out dist

# Compilar el proyecto ignorando errores
echo "Compilando el proyecto..."
NODE_ENV=production NEXT_IGNORE_ESLINT=1 NEXT_IGNORE_TYPE_CHECKS=1 npx next build || true

# Verificar si se creó la carpeta .next/standalone
if [ -d ".next/standalone" ]; then
  echo "Compilación completada con éxito."
  echo "Los archivos están listos para ser copiados al servidor Apache."
  echo ""
  echo "Para implementar en el servidor Apache, ejecuta los siguientes comandos:"
  echo ""
  echo "sudo rm -rf /var/www/html/*"
  echo "sudo cp -r .next/standalone/* /var/www/html/"
  echo "sudo cp -r .next/static /var/www/html/.next/"
  echo "sudo cp -r public /var/www/html/"
  echo ""
  echo "Luego, crea un archivo server.js en /var/www/html/ con el siguiente contenido:"
  echo ""
  echo "const { createServer } = require('http');"
  echo "const { parse } = require('url');"
  echo "const next = require('next');"
  echo ""
  echo "const dev = false;"
  echo "const app = next({ dev });"
  echo "const handle = app.getRequestHandler();"
  echo ""
  echo "app.prepare().then(() => {"
  echo "  createServer((req, res) => {"
  echo "    const parsedUrl = parse(req.url, true);"
  echo "    handle(req, res, parsedUrl);"
  echo "  }).listen(3000, (err) => {"
  echo "    if (err) throw err;"
  echo "    console.log('> Ready on http://localhost:3000');"
  echo "  });"
  echo "});"
  echo ""
  echo "Finalmente, configura Apache para redirigir al servidor Next.js:"
  echo ""
  echo "sudo a2enmod proxy proxy_http"
  echo "sudo systemctl restart apache2"
  echo ""
  echo "Y crea un archivo de configuración en /etc/apache2/sites-available/nextjs.conf:"
  echo ""
  echo "<VirtualHost *:80>"
  echo "    ServerAdmin webmaster@localhost"
  echo "    DocumentRoot /var/www/html"
  echo ""
  echo "    ProxyPreserveHost On"
  echo "    ProxyPass / http://localhost:3000/"
  echo "    ProxyPassReverse / http://localhost:3000/"
  echo ""
  echo "    ErrorLog \${APACHE_LOG_DIR}/error.log"
  echo "    CustomLog \${APACHE_LOG_DIR}/access.log combined"
  echo "</VirtualHost>"
  echo ""
  echo "Luego, habilita la configuración y reinicia Apache:"
  echo ""
  echo "sudo a2ensite nextjs.conf"
  echo "sudo systemctl restart apache2"
  echo ""
  echo "Para iniciar el servidor Next.js, ejecuta:"
  echo ""
  echo "cd /var/www/html"
  echo "node server.js"
else
  echo "Error: No se encontró la carpeta .next/standalone."
  echo "La compilación no se completó correctamente."
  exit 1
fi
