{"name": "platzi-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "NODE_ENV=production NEXT_IGNORE_ESLINT=1 NEXT_IGNORE_TYPE_CHECKS=1 next build", "export": "next export", "build:apache": "npm run build && rm -rf /var/www/html/* && cp -r build/* /var/www/html/", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@paypal/react-paypal-js": "^8.8.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.4", "@types/ws": "^8.18.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.6.0", "events": "^3.3.0", "framer-motion": "^12.6.3", "lucide-react": "^0.475.0", "next": "^15.2.0", "next-themes": "^0.4.6", "nodemailer": "^6.10.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-image-crop": "^11.0.10", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "uuid": "^11.1.0", "ws": "^8.18.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}