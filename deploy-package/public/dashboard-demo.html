<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Flasti Microtrabajos - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --background: 220 33% 6%;
            --foreground: 230 10% 95%;
            --card: 220 40% 8%;
            --card-foreground: 230 10% 95%;
            --primary: 280 85% 65%;
            --primary-foreground: 230 10% 95%;
            --secondary: 220 40% 12%;
            --secondary-foreground: 230 10% 95%;
            --muted: 220 40% 14%;
            --muted-foreground: 220 10% 70%;
            --accent: 45 80% 50%;
            --accent-foreground: 230 10% 95%;
            --border: 220 40% 14%;
        }

        body {
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .glass-card {
            background-color: hsla(var(--card), 0.6);
            backdrop-filter: blur(12px);
            border: 1px solid hsla(var(--border), 0.2);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .gradient-bg {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1), rgba(236, 72, 153, 0.1), rgba(250, 204, 21, 0.1));
        }

        .gradient-text {
            background: linear-gradient(to right, #9333ea, #ec4899, #f97316, #facc15);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .progress-bar {
            background: linear-gradient(to right, #9333ea, #ec4899);
        }

        .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        .animate-fadeIn {
            animation: fadeIn 0.5s ease-out forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .delay-100 {
            animation-delay: 0.1s;
        }

        .delay-200 {
            animation-delay: 0.2s;
        }

        .delay-300 {
            animation-delay: 0.3s;
        }

        .delay-400 {
            animation-delay: 0.4s;
        }

        .particles-bg {
            background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 30px 30px;
            opacity: 0.2;
        }

        .gradient-border {
            position: relative;
            border-radius: 12px;
            padding: 1px;
            background: linear-gradient(90deg, #9333ea, #ec4899, #facc15, #9333ea);
            background-size: 300% 100%;
            animation: gradientFlow 15s ease infinite;
        }

        @keyframes gradientFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .task-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-easy {
            background-color: rgba(34, 197, 94, 0.2);
            color: rgb(34, 197, 94);
        }

        .badge-medium {
            background-color: rgba(250, 204, 21, 0.2);
            color: rgb(250, 204, 21);
        }

        .badge-hard {
            background-color: rgba(236, 72, 153, 0.2);
            color: rgb(236, 72, 153);
        }

        .task-reward {
            position: absolute;
            bottom: 10px;
            right: 10px;
            padding: 2px 8px;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            background-color: rgba(147, 51, 234, 0.2);
            color: rgb(147, 51, 234);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="w-full py-4 border-b border-white/10 bg-black/50 backdrop-blur-md relative z-10">
        <div class="container mx-auto px-4 flex items-center justify-between">
            <div class="flex items-center gap-3">
                <div class="text-2xl font-bold gradient-text">Flasti</div>
                <div class="hidden md:flex items-center gap-2 ml-4 text-sm bg-white/5 px-3 py-1 rounded-full">
                    <i class="fas fa-globe text-blue-400"></i>
                    <span>Acceso Global</span>
                </div>
            </div>
            
            <div class="flex items-center gap-4">
                <div class="text-right hidden sm:block">
                    <div class="flex items-center justify-end gap-1">
                        <p class="font-semibold">$1,240.00 USDC</p>
                        <button class="text-white/60">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <p class="text-sm text-white/60">$1,240.00 USD</p>
                </div>
                
                <button class="text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-bell"></i>
                </button>
                
                <div class="relative">
                    <button class="h-10 w-10 rounded-full bg-gradient-to-r from-purple-600 to-pink-500 flex items-center justify-center text-white font-bold relative cursor-pointer">
                        J
                        <span class="absolute -bottom-1 -right-1 w-3 h-3 rounded-full bg-green-500 border-2 border-gray-900"></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- Sidebar -->
        <div class="w-20 border-r border-white/10 min-h-screen bg-black/30 hidden sm:flex flex-col items-center py-6 gap-6">
            <a href="#" class="w-10 h-10 rounded-lg bg-purple-600/20 flex items-center justify-center text-purple-500">
                <i class="fas fa-th-large"></i>
            </a>
            
            <a href="#" class="w-10 h-10 rounded-lg hover:bg-purple-600/10 flex items-center justify-center text-white/60 hover:text-purple-500 transition-colors">
                <i class="fas fa-tasks"></i>
            </a>
            
            <a href="#" class="w-10 h-10 rounded-lg hover:bg-purple-600/10 flex items-center justify-center text-white/60 hover:text-purple-500 transition-colors">
                <i class="fas fa-trophy"></i>
            </a>
            
            <a href="#" class="w-10 h-10 rounded-lg hover:bg-purple-600/10 flex items-center justify-center text-white/60 hover:text-purple-500 transition-colors">
                <i class="fas fa-wallet"></i>
            </a>
            
            <a href="#" class="w-10 h-10 rounded-lg hover:bg-purple-600/10 flex items-center justify-center text-white/60 hover:text-purple-500 transition-colors">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-4 md:p-8">
            <div class="max-w-5xl mx-auto">
                <!-- Balance Card -->
                <div class="glass-card rounded-xl p-6 mb-8 border border-white/10 relative z-0 hover-lift gradient-bg">
                    <h2 class="text-sm text-white/60 uppercase font-medium mb-2">Balance</h2>

                    <div class="flex items-center gap-2 mb-1">
                        <h3 class="text-4xl font-bold">$1,240.00</h3>
                        <span class="text-lg">USDC</span>
                        <button class="text-white/60 hover:text-white transition-colors">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <p class="text-white/70 mb-4">$1,240.00 USD</p>

                    <div class="flex items-center gap-3 mb-4">
                        <button class="bg-gradient-to-r from-purple-600 to-pink-500 hover:opacity-90 transition-opacity px-4 py-2 rounded-lg flex items-center gap-2 text-white">
                            <i class="fas fa-minus-circle"></i>
                            Retirar
                        </button>
                    </div>

                    <div class="flex items-center gap-1 text-sm">
                        <span>1 USDC = 1 USD</span>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                    <!-- Microtrabajos Completados -->
                    <div class="glass-card p-4 rounded-xl animate-fadeIn delay-100 hover-lift">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm text-white/60 font-medium">Microtrabajos Completados</h3>
                            <div class="w-8 h-8 rounded-full bg-purple-600/10 flex items-center justify-center">
                                <i class="fas fa-check-circle text-purple-600"></i>
                            </div>
                        </div>

                        <div class="flex items-end gap-1">
                            <span class="text-2xl font-bold">1,290</span>
                        </div>

                        <div class="mt-2 text-xs text-white/60">
                            Última actualización: 14:32:45
                        </div>
                    </div>

                    <!-- Ganancias Totales -->
                    <div class="glass-card p-4 rounded-xl animate-fadeIn delay-200 hover-lift">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm text-white/60 font-medium">Ganancias Totales</h3>
                            <div class="w-8 h-8 rounded-full bg-pink-500/10 flex items-center justify-center">
                                <i class="fas fa-dollar-sign text-pink-500"></i>
                            </div>
                        </div>

                        <div class="flex items-end gap-1">
                            <span class="text-2xl font-bold">$1,240.00</span>
                        </div>

                        <div class="mt-2 text-xs text-white/60">
                            Última actualización: 14:32:45
                        </div>
                    </div>

                    <!-- Promedio Diario -->
                    <div class="glass-card p-4 rounded-xl animate-fadeIn delay-300 hover-lift">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm text-white/60 font-medium">Promedio Diario</h3>
                            <div class="w-8 h-8 rounded-full bg-yellow-500/10 flex items-center justify-center">
                                <i class="fas fa-chart-line text-yellow-500"></i>
                            </div>
                        </div>

                        <div class="flex items-end gap-1">
                            <span class="text-2xl font-bold">$42.50</span>
                        </div>

                        <div class="mt-2 text-xs text-white/60">
                            Últimos 7 días
                        </div>
                    </div>

                    <!-- Última Actividad -->
                    <div class="glass-card p-4 rounded-xl animate-fadeIn delay-400 hover-lift">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm text-white/60 font-medium">Última Actividad</h3>
                            <div class="w-8 h-8 rounded-full bg-blue-500/10 flex items-center justify-center">
                                <i class="fas fa-clock text-blue-500"></i>
                            </div>
                        </div>

                        <div class="flex items-end gap-1">
                            <span class="text-2xl font-bold">14:30</span>
                        </div>

                        <div class="mt-2 text-xs text-white/60">
                            Microtrabajo completado
                        </div>
                    </div>
                </div>

                <!-- Microtrabajos Disponibles -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-bold">Microtrabajos Disponibles</h2>
                        <button class="text-sm text-purple-400 hover:text-purple-300 transition-colors flex items-center gap-1">
                            Ver todos
                            <i class="fas fa-arrow-right text-xs"></i>
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Microtrabajo 1 -->
                        <div class="glass-card p-5 rounded-xl relative hover-lift">
                            <span class="task-badge badge-easy">Fácil</span>
                            <span class="task-reward">$2.50</span>
                            
                            <div class="flex items-start gap-3 mb-4">
                                <div class="w-10 h-10 rounded-full bg-purple-600/10 flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-file-alt text-purple-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold mb-1">Transcripción de Audio</h3>
                                    <p class="text-sm text-white/70">Transcribe un audio de 2 minutos a texto</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between text-xs text-white/60 mb-4">
                                <span>Tiempo estimado: 10 min</span>
                                <span>Disponible: 24h</span>
                            </div>
                            
                            <button class="w-full bg-gradient-to-r from-purple-600 to-pink-500 hover:opacity-90 transition-opacity py-2 rounded-lg text-sm">
                                Comenzar tarea
                            </button>
                        </div>
                        
                        <!-- Microtrabajo 2 -->
                        <div class="glass-card p-5 rounded-xl relative hover-lift">
                            <span class="task-badge badge-medium">Medio</span>
                            <span class="task-reward">$5.00</span>
                            
                            <div class="flex items-start gap-3 mb-4">
                                <div class="w-10 h-10 rounded-full bg-pink-500/10 flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-image text-pink-500"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold mb-1">Etiquetado de Imágenes</h3>
                                    <p class="text-sm text-white/70">Etiqueta 50 imágenes según las instrucciones</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between text-xs text-white/60 mb-4">
                                <span>Tiempo estimado: 20 min</span>
                                <span>Disponible: 12h</span>
                            </div>
                            
                            <button class="w-full bg-gradient-to-r from-purple-600 to-pink-500 hover:opacity-90 transition-opacity py-2 rounded-lg text-sm">
                                Comenzar tarea
                            </button>
                        </div>
                        
                        <!-- Microtrabajo 3 -->
                        <div class="glass-card p-5 rounded-xl relative hover-lift">
                            <span class="task-badge badge-hard">Avanzado</span>
                            <span class="task-reward">$12.00</span>
                            
                            <div class="flex items-start gap-3 mb-4">
                                <div class="w-10 h-10 rounded-full bg-yellow-500/10 flex items-center justify-center flex-shrink-0">
                                    <i class="fas fa-code text-yellow-500"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold mb-1">Revisión de Código</h3>
                                    <p class="text-sm text-white/70">Revisa y corrige errores en un fragmento de código</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between text-xs text-white/60 mb-4">
                                <span>Tiempo estimado: 45 min</span>
                                <span>Disponible: 48h</span>
                            </div>
                            
                            <button class="w-full bg-gradient-to-r from-purple-600 to-pink-500 hover:opacity-90 transition-opacity py-2 rounded-lg text-sm">
                                Comenzar tarea
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Historial de Microtrabajos -->
                <div class="glass-card p-6 rounded-xl animate-fadeIn delay-500 hover-lift mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">Historial de Microtrabajos</h3>
                        <div class="flex items-center gap-1 text-xs text-white/60">
                            <i class="fas fa-clock text-xs"></i>
                            <span>Últimas 24 horas</span>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex gap-3 items-start animate-fadeIn">
                            <div class="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center mt-0.5">
                                <i class="fas fa-check text-green-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm">Transcripción de Audio Completada</p>
                                    <p class="text-sm font-semibold text-green-400">+$2.50</p>
                                </div>
                                <p class="text-xs text-white/60 mt-1">2 minutos atrás</p>
                            </div>
                        </div>

                        <div class="flex gap-3 items-start animate-fadeIn">
                            <div class="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center mt-0.5">
                                <i class="fas fa-check text-green-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm">Etiquetado de Imágenes Completado</p>
                                    <p class="text-sm font-semibold text-green-400">+$5.00</p>
                                </div>
                                <p class="text-xs text-white/60 mt-1">1 hora atrás</p>
                            </div>
                        </div>

                        <div class="flex gap-3 items-start animate-fadeIn">
                            <div class="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center mt-0.5">
                                <i class="fas fa-check text-green-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm">Revisión de Código Completada</p>
                                    <p class="text-sm font-semibold text-green-400">+$12.00</p>
                                </div>
                                <p class="text-xs text-white/60 mt-1">3 horas atrás</p>
                            </div>
                        </div>

                        <div class="flex gap-3 items-start animate-fadeIn">
                            <div class="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center mt-0.5">
                                <i class="fas fa-check text-green-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm">Encuesta de Producto Completada</p>
                                    <p class="text-sm font-semibold text-green-400">+$3.50</p>
                                </div>
                                <p class="text-xs text-white/60 mt-1">5 horas atrás</p>
                            </div>
                        </div>

                        <div class="flex gap-3 items-start animate-fadeIn">
                            <div class="w-8 h-8 rounded-full bg-yellow-500/10 flex items-center justify-center mt-0.5">
                                <i class="fas fa-hand-holding-usd text-yellow-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm">Retiro a PayPal</p>
                                    <p class="text-sm font-semibold text-yellow-400">-$120.00</p>
                                </div>
                                <p class="text-xs text-white/60 mt-1">12 horas atrás</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Level Progress -->
                <div class="glass-card p-6 rounded-xl animate-fadeIn delay-300 relative overflow-hidden hover-lift mb-8">
                    <div class="absolute inset-0 particles-bg"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold">Tu Progreso de Nivel</h3>
                            <div class="flex items-center gap-2 px-3 py-1 rounded-full bg-purple-600/10 text-purple-400 text-sm">
                                <span>Nivel 2</span>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>

                        <div class="mb-6 gradient-border">
                            <div class="bg-black/80 backdrop-blur-sm p-4 rounded-xl">
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 rounded-full bg-purple-600/10 flex items-center justify-center">
                                        <i class="fas fa-star text-purple-600 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold">Nivel Intermedio</h3>
                                        <p class="text-sm text-white/70">Nivel 2</p>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-check text-green-500"></i>
                                        <span>Acceso a tareas de mayor valor</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-check text-green-500"></i>
                                        <span>Bonificación del 10% en todas las tareas</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-check text-green-500"></i>
                                        <span>Prioridad en tareas exclusivas</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Barra de progreso -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-white/70">Progreso hacia Nivel 3</span>
                                <span class="text-sm font-medium">65%</span>
                            </div>
                            <div class="h-3 bg-white/10 rounded-full overflow-hidden">
                                <div class="h-full rounded-full progress-bar" style="width: 65%"></div>
                            </div>
                            <div class="flex items-center justify-between mt-2 text-xs text-white/60">
                                <span>Tareas completadas: 1,290</span>
                                <span>Objetivo: 2,000 tareas</span>
                            </div>
                        </div>

                        <!-- Próximo nivel -->
                        <div class="bg-black/50 backdrop-blur-sm p-4 rounded-xl border border-white/5 hover:border-purple-500/20 transition-all duration-300 hover-lift">
                            <div class="flex items-center gap-4 mb-3">
                                <div class="w-10 h-10 rounded-full bg-pink-500/10 flex items-center justify-center">
                                    <i class="fas fa-crown text-pink-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold">Nivel Experto</h3>
                                    <p class="text-sm text-white/70">Próximo nivel</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-2 text-sm mb-2">
                                <i class="fas fa-arrow-right"></i>
                                <span>Bonificación del 20% en todas las tareas</span>
                            </div>

                            <div class="flex items-center gap-2 text-sm">
                                <i class="fas fa-arrow-right"></i>
                                <span>Acceso a tareas premium de alto valor</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simular actividad en tiempo real
        setTimeout(() => {
            const statsElement = document.querySelector('.glass-card:nth-child(1) .text-2xl');
            if (statsElement) {
                const currentValue = parseInt(statsElement.textContent.replace(',', ''));
                statsElement.textContent = (currentValue + 1).toLocaleString();
                
                const updateElement = statsElement.closest('.glass-card').querySelector('.text-xs.text-white\\/60');
                if (updateElement) {
                    updateElement.textContent = 'Última actualización: ' + new Date().toLocaleTimeString();
                }
            }
        }, 5000);
    </script>
</body>
</html>
                                </div>
                                <p class="text-xs text-white/60 mt-1">5 horas atrás</p>
                            </div>
                        </div>

                        <div class="flex gap-3 items-start animate-fadeIn">
                            <div class="w-8 h-8 rounded-full bg-yellow-500/10 flex items-center justify-center mt-0.5">
                                <i class="fas fa-hand-holding-usd text-yellow-500 text-sm"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <p class="text-sm">Retiro a PayPal</p>
                                    <p class="text-sm font-semibold text-yellow-400">-$120.00</p>
                                </div>
                                <p class="text-xs text-white/60 mt-1">12 horas atrás</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Level Progress -->
                <div class="glass-card p-6 rounded-xl animate-fadeIn delay-300 relative overflow-hidden hover-lift mb-8">
                    <div class="absolute inset-0 particles-bg"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold">Tu Progreso de Nivel</h3>
                            <div class="flex items-center gap-2 px-3 py-1 rounded-full bg-purple-600/10 text-purple-400 text-sm">
                                <span>Nivel 2</span>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>

                        <div class="mb-6 gradient-border">
                            <div class="bg-black/80 backdrop-blur-sm p-4 rounded-xl">
                                <div class="flex items-center gap-4 mb-4">
                                    <div class="w-12 h-12 rounded-full bg-purple-600/10 flex items-center justify-center">
                                        <i class="fas fa-star text-purple-600 text-xl"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold">Nivel Intermedio</h3>
                                        <p class="text-sm text-white/70">Nivel 2</p>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-check text-green-500"></i>
                                        <span>Acceso a tareas de mayor valor</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-check text-green-500"></i>
                                        <span>Bonificación del 10% en todas las tareas</span>
                                    </div>
                                    <div class="flex items-center gap-2">
                                        <i class="fas fa-check text-green-500"></i>
                                        <span>Prioridad en tareas exclusivas</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Barra de progreso -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-white/70">Progreso hacia Nivel 3</span>
                                <span class="text-sm font-medium">65%</span>
                            </div>
                            <div class="h-3 bg-white/10 rounded-full overflow-hidden">
                                <div class="h-full rounded-full progress-bar" style="width: 65%"></div>
                            </div>
                            <div class="flex items-center justify-between mt-2 text-xs text-white/60">
                                <span>Tareas completadas: 1,290</span>
                                <span>Objetivo: 2,000 tareas</span>
                            </div>
                        </div>

                        <!-- Próximo nivel -->
                        <div class="bg-black/50 backdrop-blur-sm p-4 rounded-xl border border-white/5 hover:border-purple-500/20 transition-all duration-300 hover-lift">
                            <div class="flex items-center gap-4 mb-3">
                                <div class="w-10 h-10 rounded-full bg-pink-500/10 flex items-center justify-center">
                                    <i class="fas fa-crown text-pink-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold">Nivel Experto</h3>
                                    <p class="text-sm text-white/70">Próximo nivel</p>
                                </div>
                            </div>

                            <div class="flex items-center gap-2 text-sm mb-2">
                                <i class="fas fa-arrow-right"></i>
                                <span>Bonificación del 20% en todas las tareas</span>
                            </div>

                            <div class="flex items-center gap-2 text-sm">
                                <i class="fas fa-arrow-right"></i>
                                <span>Acceso a tareas premium de alto valor</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simular actividad en tiempo real
        setTimeout(() => {
            const statsElement = document.querySelector('.glass-card:nth-child(1) .text-2xl');
            if (statsElement) {
                const currentValue = parseInt(statsElement.textContent.replace(',', ''));
                statsElement.textContent = (currentValue + 1).toLocaleString();
                
                const updateElement = statsElement.closest('.glass-card').querySelector('.text-xs.text-white\\/60');
                if (updateElement) {
                    updateElement.textContent = 'Última actualización: ' + new Date().toLocaleTimeString();
                }
            }
        }, 5000);
    </script>
</body>
</html>
