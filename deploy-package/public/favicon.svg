<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" rx="128" fill="url(#paint0_linear)" />
  <path d="M128 160H384V192H128V160Z" fill="white"/>
  <path d="M128 240H384V272H128V240Z" fill="white"/>
  <path d="M128 320H256V352H128V320Z" fill="white"/>
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="512" y2="512" gradientUnits="userSpaceOnUse">
      <stop stop-color="#9333EA"/>
      <stop offset="1" stop-color="#EC4899"/>
    </linearGradient>
  </defs>
</svg>
