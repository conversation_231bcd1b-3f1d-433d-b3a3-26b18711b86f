<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="welcomeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3b82f6" />
      <stop offset="50%" stop-color="#8b5cf6" />
      <stop offset="100%" stop-color="#ec4899" />
    </linearGradient>
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#3b82f6" flood-opacity="0.3"/>
    </filter>
    <clipPath id="circleClip">
      <circle cx="150" cy="150" r="120"/>
    </clipPath>
  </defs>

  <!-- Background -->
  <circle cx="150" cy="150" r="150" fill="#f8fafc" opacity="0.05"/>
  <circle cx="150" cy="150" r="120" fill="url(#welcomeGradient)" opacity="0.1"/>

  <!-- Decorative elements -->
  <circle cx="150" cy="150" r="100" stroke="url(#welcomeGradient)" stroke-width="2" stroke-dasharray="4 4" opacity="0.6"/>
  <circle cx="150" cy="150" r="80" stroke="url(#welcomeGradient)" stroke-width="1.5" opacity="0.4"/>

  <!-- Stars -->
  <path d="M50,60 L55,70 L65,72 L55,80 L58,90 L50,85 L42,90 L45,80 L35,72 L45,70 Z" fill="#ec4899" opacity="0.7" filter="url(#shadow)"/>
  <path d="M250,60 L255,70 L265,72 L255,80 L258,90 L250,85 L242,90 L245,80 L235,72 L245,70 Z" fill="#8b5cf6" opacity="0.7" filter="url(#shadow)"/>
  <path d="M50,240 L55,250 L65,252 L55,260 L58,270 L50,265 L42,270 L45,260 L35,252 L45,250 Z" fill="#3b82f6" opacity="0.7" filter="url(#shadow)"/>
  <path d="M250,240 L255,250 L265,252 L255,260 L258,270 L250,265 L242,270 L245,260 L235,252 L245,250 Z" fill="#ec4899" opacity="0.7" filter="url(#shadow)"/>

  <!-- Main illustration -->
  <g clip-path="url(#circleClip)">
    <!-- Person silhouette -->
    <path d="M150,100 C170,100 185,115 185,135 C185,155 170,170 150,170 C130,170 115,155 115,135 C115,115 130,100 150,100Z" fill="#3b82f6" opacity="0.8"/>
    <path d="M110,170 C110,170 110,210 150,210 C190,210 190,170 190,170" stroke="#3b82f6" stroke-width="8" opacity="0.8" stroke-linecap="round"/>

    <!-- Decorative elements -->
    <circle cx="120" cy="220" r="40" fill="#8b5cf6" opacity="0.2"/>
    <circle cx="180" cy="220" r="40" fill="#ec4899" opacity="0.2"/>
    <circle cx="150" cy="80" r="40" fill="#3b82f6" opacity="0.2"/>
  </g>

  <!-- Welcome text -->
  <text x="150" y="240" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="url(#welcomeGradient)" text-anchor="middle" filter="url(#shadow)">¡Bienvenido!</text>
</svg>
