/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

body {
    min-height: 100vh;
    background: linear-gradient(135deg, #13111C 0%, #1E1B2E 100%);
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 0;
    position: relative;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 20%, rgba(147, 51, 234, 0.05) 0%, transparent 40%),
                radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 40%);
    pointer-events: none;
    z-index: 1;
}

.container {
    width: 100%;
    max-width: 1200px;
    text-align: center;
    position: relative;
    z-index: 2;
    padding-bottom: 0;
}

/* Navegación principal */
.main-nav {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(19, 17, 28, 0.9);
    backdrop-filter: blur(10px);
    z-index: 100;
    border-bottom: 1px solid rgba(168, 85, 247, 0.2);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0.75rem 2rem;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 10px;
}

.nav-logo-img {
    width: 32px;
    height: 32px;
}

.nav-logo h1 {
    font-size: 1.5rem;
    margin: 0;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    padding: 0.5rem 0;
}

.nav-link:hover {
    color: #fff;
}

.nav-buttons {
    display: flex;
    align-items: center;
}

.login-button {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 6px;
    color: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

/* Ocultar el botón del menú hamburguesa en escritorio */
.mobile-menu-toggle {
    display: none;
}

.login-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#user-name {
    font-weight: 500;
}

.dashboard-link {
    color: #a855f7;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.dashboard-link:hover {
    text-decoration: underline;
}

/* Sección Hero */
.hero-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 0 0 30px 30px;
    padding: 8rem 2rem 4rem;
    margin-bottom: 3rem;
    border: 2px solid transparent;
    border-top: none;
    background-clip: padding-box;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4), inset 0 0 0 2px rgba(168, 85, 247, 0.4);
    background-image: linear-gradient(45deg, rgba(168, 85, 247, 0.1), rgba(255, 107, 107, 0.1));
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-block;
    background: #ff6b6b;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.ai-text {
    color: #a855f7;
    background: linear-gradient(90deg, #a855f7, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #fff;
    margin-bottom: 1rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Sección del generador */
.generator-section {
    padding: 2rem;
    margin-bottom: 4rem;
}

.input-container {
    display: flex;
    gap: 8px;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

#prompt-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

#prompt-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.4);
}

#prompt-input::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

#generate-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
}

#generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(168, 85, 247, 0.4);
}

.generated-image-container {
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 3rem;
    width: 100%;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(168, 85, 247, 0.3);
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.2);
    z-index: 10;
    min-height: 200px;
}

#generated-image {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 500px;
    object-fit: contain;
    border-radius: 8px;
    display: block;
    transition: opacity 0.3s ease;
    margin: 0 0 1rem 0;
    opacity: 1;
}

.image-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 0.6rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.action-icon {
    font-size: 1.2rem;
}

/* Sección de información */
.info-section {
    padding: 4rem 2rem;
    margin-bottom: 4rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 30px;
}

.section-title {
    font-size: 2rem;
    margin-bottom: 3rem;
    color: #fff;
    text-align: center;
}

.steps-container {
    display: flex;
    justify-content: space-between;
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.step-item {
    flex: 1;
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(168, 85, 247, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.step-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(168, 85, 247, 0.2);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    border-radius: 50%;
    font-size: 1.2rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
}

.step-title {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #fff;
}

.step-description {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
}

/* Sección de ejemplos */
.examples-section {
    padding: 4rem 2rem;
    margin-bottom: 4rem;
}

.examples-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.example-item {
    border-radius: 16px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(168, 85, 247, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.example-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
    border-color: rgba(168, 85, 247, 0.4);
}

.example-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.example-img:hover {
    transform: scale(1.03);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.example-prompt {
    padding: 1rem;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    text-align: center;
    font-style: italic;
}

/* Sección de precios */
.pricing-section {
    padding: 4rem 2rem;
    margin-bottom: 4rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 30px;
}

.pricing-container {
    display: flex;
    justify-content: center;
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.pricing-card {
    flex: 1;
    max-width: 350px;
    padding: 2.5rem;
    border-radius: 16px;
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.free-plan {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(168, 85, 247, 0.2);
}

.premium-plan {
    background: rgba(168, 85, 247, 0.1);
    border: 1px solid rgba(168, 85, 247, 0.4);
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.3);
}

.plan-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.plan-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
}

.plan-price {
    font-size: 3rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.5rem;
}

.plan-period {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 2rem;
}

.plan-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.plan-features li {
    padding: 0.5rem 0;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
    position: relative;
    padding-left: 1.5rem;
}

.plan-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #a855f7;
    font-weight: bold;
}

.plan-description {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 1rem;
}

.premium-cta-btn {
    width: 100%;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    border: none;
    padding: 0.8rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.premium-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
}

/* Footer */
.main-footer {
    background: rgba(19, 17, 28, 0.8);
    padding: 4rem 2rem 2rem;
    margin-top: 4rem;
    border-top: 1px solid rgba(168, 85, 247, 0.2);
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

.footer-container {
    display: flex;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto 3rem;
}

.footer-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.footer-logo-img {
    width: 40px;
    height: 40px;
    margin-bottom: 1rem;
}

.footer-logo h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.footer-links {
    display: flex;
    gap: 4rem;
}

.footer-column h4 {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #fff;
}

.footer-column ul {
    list-style: none;
}

.footer-column li {
    margin-bottom: 0.8rem;
}

.footer-column a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.footer-column a:hover {
    color: #fff;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.copyright {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.social-links {
    display: flex;
    gap: 1.5rem;
}

.social-link {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
}

.social-link:hover {
    color: #fff;
}

/* Popup de bienvenida */
.benefits-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(19, 17, 28, 0.95);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
}

.benefits-popup.active {
    display: flex;
    opacity: 1;
}

.benefits-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 2rem;
    max-width: 600px;
    width: 90%;
    border: 1px solid rgba(168, 85, 247, 0.3);
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.2);
}

.benefits-content h2 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(90deg, #a855f7, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.benefits-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    margin-bottom: 2rem;
}

.benefits-list {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    text-align: left;
}

.benefit-icon {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: rgba(168, 85, 247, 0.1);
    border-radius: 5px;
}

.benefit-item h3 {
    font-size: 0.85rem;
    margin-bottom: 0.2rem;
    color: #fff;
    font-weight: 500;
}

.benefit-item p {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1.3;
}

.close-benefits {
    width: 100%;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    border: none;
    padding: 0.6rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.close-benefits:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
}

.beta-disclaimer {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 1.5rem;
    padding: 0.75rem;
    background: rgba(255, 107, 107, 0.1);
    border-radius: 8px;
}

/* Popup de premium */
.premium-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(19, 17, 28, 0.95);
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: all 0.3s ease;
}

.premium-popup.active {
    display: flex;
    opacity: 1;
}

.premium-content {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    border: 1px solid rgba(168, 85, 247, 0.3);
    box-shadow: 0 0 30px rgba(168, 85, 247, 0.2);
    text-align: center;
    position: relative;
    margin-bottom: 1rem;
}

.premium-content h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(90deg, #a855f7, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.premium-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.premium-features {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.premium-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-align: left;
}

.premium-icon {
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    color: #fff;
}

.premium-feature p {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.3;
    margin: 0;
}

.premium-feature p strong {
    color: white;
    font-weight: 600;
}

.premium-price {
    margin-bottom: 1.5rem;
}

.price-tag {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 0.2rem;
    margin: 0;
}

.price-currency {
    font-size: 1.2rem;
    color: #fff;
    margin-right: 0.2rem;
}

.price-amount {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
}

.price-period {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-left: 0.5rem;
}

.price-lifetime {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.8rem;
    margin: 0.5rem 0 0 0;
}

.premium-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.get-premium-btn {
    width: 100%;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    border: none;
    padding: 0.6rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.get-premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
}

.close-premium-btn {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.7);
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.close-premium-btn:hover {
    background: rgba(255, 255, 255, 0.05);
    color: white;
}

.premium-footer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

.premium-footer .new-badge {
    margin-bottom: 0.5rem;
}

.premium-footer h1 {
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
}

.login-premium-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    cursor: pointer;
    text-decoration: underline;
    padding: 0.5rem;
    transition: color 0.2s ease;
}

.login-premium-btn:hover {
    color: white;
}

/* Media queries */
@media (max-width: 992px) {
    .steps-container, .pricing-container {
        flex-direction: column;
        align-items: center;
    }

    .step-item, .pricing-card {
        width: 100%;
        max-width: 500px;
    }

    .footer-container {
        flex-direction: column;
        gap: 3rem;
    }

    .footer-links {
        width: 100%;
        justify-content: space-between;
    }
}

@media (max-width: 768px) {
    body {
        padding: 0;
    }

    .nav-container {
        padding: 0.5rem 1rem;
        flex-wrap: wrap;
    }

    .nav-menu {
        order: 3;
        width: 100%;
        justify-content: center;
        margin-top: 0.5rem;
        gap: 1rem;
    }

    .hero-section {
        padding: 7rem 1rem 3rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .input-container {
        flex-direction: column;
        gap: 1rem;
    }

    #prompt-input {
        padding: 0.75rem 1rem;
    }

    #generate-btn {
        width: 100%;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .examples-gallery {
        grid-template-columns: 1fr;
    }

    .footer-links {
        flex-direction: column;
        gap: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        justify-content: center;
        text-align: center;
    }

    .nav-logo {
        margin-bottom: 0.5rem;
    }

    .nav-buttons {
        margin-top: 0.5rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .step-item {
        padding: 1.5rem;
    }

    .pricing-card {
        padding: 1.5rem;
    }
}
