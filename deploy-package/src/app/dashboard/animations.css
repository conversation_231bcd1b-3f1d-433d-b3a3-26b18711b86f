/* Animaciones para el dashboard */

/* Animación de entrada para las tarjetas */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animación de pulso para destacar elementos */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(147, 51, 234, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(147, 51, 234, 0);
  }
}

/* Animación de brillo para elementos importantes */
@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(147, 51, 234, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(147, 51, 234, 0.5);
  }
}

/* Animación para el fondo del dashboard */
@keyframes gradientFlow {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Animación para números que aumentan */
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animación para notificaciones */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Animación para el indicador de carga */
@keyframes loadingBar {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

/* Clases de utilidad para aplicar animaciones */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-glow {
  animation: glow 2s infinite;
}

.animate-gradientFlow {
  animation: gradientFlow 15s ease infinite;
  background-size: 400% 400%;
}

.animate-countUp {
  animation: countUp 0.5s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out forwards;
}

.animate-loadingBar {
  animation: loadingBar 2s linear forwards;
}

/* Retrasos de animación para crear efectos en cascada */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

/* Efectos de hover mejorados */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Efecto de vidrio esmerilado mejorado */
.glass-effect {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Efecto de gradiente animado para bordes */
.gradient-border {
  position: relative;
  border-radius: 12px;
  padding: 1px;
  background: linear-gradient(90deg, #9333ea, #ec4899, #facc15, #9333ea);
  background-size: 300% 100%;
  animation: gradientFlow 15s ease infinite;
}

.gradient-border::after {
  content: "";
  position: absolute;
  inset: 1px;
  border-radius: 11px;
  background-color: var(--background);
  z-index: -1;
}

/* Efecto de partículas para fondos */
.particles-bg {
  position: relative;
  overflow: hidden;
}

.particles-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 30%, rgba(147, 51, 234, 0.1) 0%, transparent 8%),
    radial-gradient(circle at 50% 70%, rgba(236, 72, 153, 0.1) 0%, transparent 8%),
    radial-gradient(circle at 80% 40%, rgba(250, 204, 21, 0.1) 0%, transparent 8%);
  background-size: 180% 180%;
  animation: gradientFlow 15s ease infinite;
  z-index: -1;
}
