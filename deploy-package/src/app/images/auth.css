/* Estilos para las páginas de autenticación */
.auth-container {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    background: linear-gradient(135deg, #13111C 0%, #1E1B2E 100%);
}

.auth-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 20%, rgba(147, 51, 234, 0.05) 0%, transparent 40%),
                radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 40%);
    pointer-events: none;
    z-index: 1;
}

.auth-card {
    width: 100%;
    max-width: 450px;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    border: 2px solid transparent;
    background-clip: padding-box;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.4), inset 0 0 0 2px rgba(168, 85, 247, 0.4);
    background-image: linear-gradient(45deg, rgba(168, 85, 247, 0.1), rgba(255, 107, 107, 0.1));
    position: relative;
    z-index: 2;
}

.auth-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.auth-logo {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.auth-card h1 {
    font-size: 2rem;
    color: #fff;
    margin: 0;
}

.auth-card h2 {
    font-size: 1.5rem;
    color: #fff;
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.4);
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #a855f7;
}

.checkbox-group label {
    margin-bottom: 0;
    font-size: 0.85rem;
}

.terms-link {
    color: #a855f7;
    text-decoration: none;
}

.terms-link:hover {
    text-decoration: underline;
}

.auth-button {
    width: 100%;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(168, 85, 247, 0.4);
}

.auth-link {
    text-align: center;
    margin-top: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.auth-link a {
    color: #a855f7;
    text-decoration: none;
    font-weight: 500;
}

.auth-link a:hover {
    text-decoration: underline;
}

.error-message {
    background: rgba(255, 107, 107, 0.1);
    color: #ff6b6b;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

/* Estilo para mensajes de éxito */
.error-message.success {
    background: rgba(74, 222, 128, 0.1);
    color: #4ade80;
    border: 1px solid rgba(74, 222, 128, 0.3);
}

@media (max-width: 768px) {
    .auth-card {
        padding: 2rem 1.5rem;
    }

    .auth-card h1 {
        font-size: 1.8rem;
    }

    .auth-card h2 {
        font-size: 1.3rem;
    }
}
