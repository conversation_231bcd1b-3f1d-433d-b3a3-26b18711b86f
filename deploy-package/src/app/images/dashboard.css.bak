/* Estilos para el panel de usuario */
body {
    padding: 0;
    overflow-x: hidden;
}

.dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #13111C 0%, #1E1B2E 100%);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(168, 85, 247, 0.2);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dashboard-logo {
    width: 32px;
    height: 32px;
}

.dashboard-header h1 {
    font-size: 1.5rem;
    margin: 0;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

#user-name {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.logout-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.15);
}

.dashboard-main {
    display: flex;
    flex: 1;
}

.dashboard-sidebar {
    width: 220px;
    background: rgba(255, 255, 255, 0.03);
    border-right: 1px solid rgba(168, 85, 247, 0.2);
    padding: 2rem 0;
}

.dashboard-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dashboard-nav li {
    margin-bottom: 0.5rem;
}

.dashboard-nav a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.dashboard-nav li.active a {
    color: white;
    background: rgba(168, 85, 247, 0.1);
    border-left: 3px solid #a855f7;
}

.dashboard-nav a:hover {
    color: white;
    background: rgba(255, 255, 255, 0.05);
}

.dashboard-content {
    flex: 1;
    padding: 2rem;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.dashboard-section h2 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.5rem;
}

.input-container {
    display: flex;
    gap: 8px;
    margin-bottom: 1.5rem;
    max-width: 600px;
}

#prompt-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1rem;
    transition: all 0.3s ease;
}

#prompt-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 2px rgba(168, 85, 247, 0.4);
}

#generate-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 0 20px rgba(168, 85, 247, 0.5);
}

#generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(168, 85, 247, 0.4);
}

.generated-image-container {
    border-radius: 16px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    width: 100%;
    max-width: 800px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(168, 85, 247, 0.3);
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.2);
    min-height: 200px;
    height: auto;
}

#generated-image {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 600px;
    object-fit: contain;
    border-radius: 8px;
    display: block;
    transition: opacity 0.3s ease;
    margin: 0;
    opacity: 1;
}

.download-btn, .save-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 16px;
    transition: all 0.2s ease;
    backdrop-filter: blur(4px);
}

.download-btn {
    right: 16px;
}

.save-btn {
    right: 60px;
    width: auto;
    padding: 0 12px;
    font-size: 0.85rem;
}

.download-btn:hover, .save-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.history-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.history-item {
    border-radius: 12px;
    overflow: hidden;
    position: relative;
    aspect-ratio: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(168, 85, 247, 0.2);
}

.history-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.history-item:hover img {
    transform: scale(1.05);
}

.history-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.history-item:hover .history-actions {
    opacity: 1;
}

.history-action-btn {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    backdrop-filter: blur(4px);
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: rgba(255, 255, 255, 0.5);
    background: rgba(255, 255, 255, 0.03);
    border-radius: 12px;
    border: 1px dashed rgba(168, 85, 247, 0.2);
}

.profile-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1.5rem;
    max-width: 600px;
}

.profile-field {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-field:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.profile-field label {
    width: 120px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.premium-badge {
    display: inline-block;
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.plan-info {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px dashed rgba(255, 255, 255, 0.1);
}

.plan-info p {
    margin: 0 0 0.5rem 0;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

.upgrade-btn {
    background: linear-gradient(45deg, #a855f7, #ff6b6b);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 0.5rem;
}

.upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
}

.dashboard-footer {
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.03);
    border-top: 1px solid rgba(168, 85, 247, 0.2);
    text-align: center;
}

.copyright {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

@media (max-width: 768px) {
    .dashboard-main {
        flex-direction: column;
    }

    .dashboard-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid rgba(168, 85, 247, 0.2);
        padding: 0;
    }

    .dashboard-nav ul {
        display: flex;
        overflow-x: auto;
    }

    .dashboard-nav li {
        margin-bottom: 0;
        margin-right: 0.5rem;
    }

    .dashboard-nav a {
        padding: 0.75rem 1rem;
        border-left: none;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
    }

    .dashboard-nav li.active a {
        border-left: none;
        border-bottom: 3px solid #a855f7;
    }

    .dashboard-content {
        padding: 1.5rem 1rem;
    }

    .input-container {
        flex-direction: column;
    }

    #generate-btn {
        width: 100%;
    }
}
