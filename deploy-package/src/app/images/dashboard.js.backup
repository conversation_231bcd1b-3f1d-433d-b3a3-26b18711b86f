// Configuración de Supabase
const SUPABASE_URL = 'https://lflxpqryawqrbpdxvmka.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxmbHhwcXJ5YXdxcmJwZHh2bWthIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MTI3NDEsImV4cCI6MjA1ODk4ODc0MX0.QMmPkbK9T7ZHZygm5eEdrNA5nTLBWx-3CRw5TdpGaB0';

// Verificar que Supabase esté disponible
let supabaseClient;
try {
    if (typeof supabase !== 'undefined') {
        supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        console.log('Cliente Supabase inicializado correctamente en dashboard');
    } else {
        console.error('Error: Supabase no está definido en dashboard');
        alert('Error al inicializar la autenticación. Por favor, recarga la página.');
    }
} catch (error) {
    console.error('Error al inicializar Supabase en dashboard:', error);
    alert('Error al inicializar la autenticación. Por favor, recarga la página.');
}

// API de Hugging Face
const API_URL = 'https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0';
const API_KEY = '*************************************';

document.addEventListener('DOMContentLoaded', async () => {
    // Verificar autenticación
    try {
        if (!supabaseClient) {
            console.error('Cliente Supabase no inicializado en dashboard');
            window.location.href = 'login.html';
            return;
        }

        const { data: { user } } = await supabaseClient.auth.getUser();

        if (!user) {
            console.log('Usuario no autenticado, redirigiendo a login...');
            window.location.href = 'login.html';
            return;
        }

        console.log('Usuario autenticado en dashboard:', user.email);
    } catch (error) {
        console.error('Error al verificar autenticación en dashboard:', error);
        window.location.href = 'login.html';
        return;
    }

    // Cargar datos del usuario
    const { data: { user } } = await supabaseClient.auth.getUser();
    loadUserData(user);

    // Configurar navegación
    setupNavigation();

    // Configurar generador de imágenes
    setupImageGenerator();

    // Cargar historial de imágenes
    loadImageHistory();

    // Configurar botón de cerrar sesión
    document.getElementById('logout-btn').addEventListener('click', handleLogout);
});

// Cargar datos del usuario
async function loadUserData(user) {
    try {
        if (!user) {
            console.error('No hay usuario para cargar datos');
            return;
        }

        const userName = document.getElementById('user-name');
        const profileName = document.getElementById('profile-name');
        const profileEmail = document.getElementById('profile-email');
        const profilePlan = document.getElementById('profile-plan');
        const imageCountElement = document.getElementById('image-count');

        if (!userName || !profileName || !profileEmail || !profilePlan) {
            console.error('Elementos del DOM no encontrados');
            return;
        }

        // Obtener datos del usuario desde Supabase
        const { data, error } = await supabaseClient
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();

        if (error) {
            console.error('Error al cargar datos del usuario desde Supabase:', error);

            // Intentar crear el perfil si no existe
            if (error.code === 'PGRST116') { // No se encontró el perfil
                console.log('Perfil no encontrado, intentando crear uno nuevo...');

                const { error: createError } = await supabaseClient
                    .from('profiles')
                    .insert([
                        {
                            id: user.id,
                            full_name: user.user_metadata?.full_name || 'Usuario',
                            email: user.email,
                            is_premium: user.user_metadata?.is_premium || false,
                            image_count: 0
                        }
                    ]);

                if (createError) {
                    console.error('Error al crear perfil:', createError);
                } else {
                    console.log('Perfil creado exitosamente');
                    // Recargar la página para obtener los datos actualizados
                    window.location.reload();
                    return;
                }
            }
        }

        // Mostrar datos del usuario
        const fullName = data?.full_name || user.user_metadata?.full_name || 'Usuario';
        const isPremium = data?.is_premium || user.user_metadata?.is_premium || false;
        const imageCount = data?.image_count || 0;

        userName.textContent = fullName;
        profileName.textContent = fullName;
        profileEmail.textContent = user.email;

        // Limpiar cualquier contenido previo en el contenedor del plan
        const planContainer = profilePlan.parentNode;
        const existingPlanInfo = planContainer.querySelector('.plan-info');
        if (existingPlanInfo) {
            planContainer.removeChild(existingPlanInfo);
        }

        // Mostrar el plan del usuario
        if (isPremium) {
            profilePlan.textContent = 'Premium';
            profilePlan.classList.add('premium-badge');

            // Si es premium, mostrar un mensaje adicional
            const planInfoElement = document.createElement('div');
            planInfoElement.className = 'plan-info';
            planInfoElement.innerHTML = '<p>Disfruta de generación ilimitada de imágenes</p>';

            // Insertar después del elemento del plan
            planContainer.appendChild(planInfoElement);
        } else {
            profilePlan.textContent = 'Gratuito';
            profilePlan.classList.remove('premium-badge');

            // Si no es premium, mostrar el contador de imágenes y un botón para actualizar
            const planInfoElement = document.createElement('div');
            planInfoElement.className = 'plan-info';
            planInfoElement.innerHTML = `
                <p>Has generado ${imageCount}/2 imágenes gratuitas</p>
                <button id="upgrade-btn" class="upgrade-btn">Actualizar a Premium</button>
            `;

            // Insertar después del elemento del plan
            planContainer.appendChild(planInfoElement);

            // Agregar evento al botón de actualizar
            const upgradeBtn = document.getElementById('upgrade-btn');
            if (upgradeBtn) {
                upgradeBtn.addEventListener('click', () => {
                    window.location.href = 'checkout.html';
                });
            }
        }

        // Actualizar contador de imágenes en localStorage para mantener sincronizado
        localStorage.setItem('flasti_image_count', imageCount.toString());

        console.log('Datos de usuario cargados correctamente:', { fullName, email: user.email, isPremium, imageCount });
    } catch (error) {
        console.error('Error inesperado al cargar datos del usuario:', error);
    }
}

// Configurar navegación
function setupNavigation() {
    const navLinks = document.querySelectorAll('.dashboard-nav a');
    const sections = document.querySelectorAll('.dashboard-section');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();

            // Remover clase active de todos los enlaces y secciones
            navLinks.forEach(l => l.parentElement.classList.remove('active'));
            sections.forEach(s => s.classList.remove('active'));

            // Agregar clase active al enlace y sección correspondiente
            link.parentElement.classList.add('active');
            const targetId = link.getAttribute('href').substring(1);
            document.getElementById(targetId).classList.add('active');
        });
    });
}

// Configurar generador de imágenes
function setupImageGenerator() {
    const promptInput = document.getElementById('prompt-input');
    const generateBtn = document.getElementById('generate-btn');
    const generatedImageContainer = document.getElementById('generated-image-container');
    const generatedImage = document.getElementById('generated-image');
    const downloadBtn = document.getElementById('download-btn');
    const saveBtn = document.getElementById('save-btn');

    let isGenerating = false;

    const resetImageContainer = () => {
        generatedImageContainer.style.display = 'none';
        generatedImage.src = '';
        generatedImage.style.opacity = '1';
    };

    const showError = (message) => {
        console.error('Error:', message);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        errorDiv.style.color = '#ff6b6b';
        errorDiv.style.marginBottom = '1rem';
        errorDiv.style.padding = '1rem';
        errorDiv.style.borderRadius = '8px';
        errorDiv.style.background = 'rgba(255, 107, 107, 0.1)';

        const container = document.querySelector('.dashboard-content');
        container.insertBefore(errorDiv, generatedImageContainer);

        setTimeout(() => errorDiv.remove(), 5000);
    };

    const setLoadingState = (loading) => {
        isGenerating = loading;
        generateBtn.disabled = loading;
        generateBtn.textContent = loading ? 'Generando...' : 'Generar';
        promptInput.disabled = loading;
        if (loading) {
            generateBtn.style.opacity = '0.7';
            generateBtn.style.cursor = 'not-allowed';
        } else {
            generateBtn.style.opacity = '1';
            generateBtn.style.cursor = 'pointer';
        }
    };

    generateBtn.addEventListener('click', async () => {
        const prompt = promptInput.value.trim();
        if (!prompt) {
            showError('Por favor, ingresa una descripción para la imagen.');
            return;
        }

        if (isGenerating) return;

        let response;
        try {
            setLoadingState(true);
            resetImageContainer();

            generatedImageContainer.style.display = 'block';
            generatedImage.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTIgMkM2LjQ3IDIgMiA2LjQ3IDIgMTJzNC40NyAxMCAxMCAxMCAxMC00LjQ3IDEwLTEwUzE3LjUzIDIgMTIgMnptMCAxOGMtNC40MSAwLTgtMy41OS04LThzMy41OS04IDgtOCA4IDMuNTkgOCA4LTMuNTkgOC04IDh6Ii8+PC9zdmc+';
            generatedImage.style.opacity = '0.5';

            response = await fetch(API_URL, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${API_KEY}`,
                    'Content-Type': 'application/json',
                    'Accept': 'image/*'
                },
                body: JSON.stringify({
                    inputs: prompt
                }),
                mode: 'cors'
            });

            if (response.status === 503) {
                const retryAfter = response.headers.get('Retry-After') || 20;
                await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
                throw new Error('El modelo se está cargando. Por favor, intenta de nuevo en unos segundos.');
            }

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API Error:', errorData);
                const errorMessage = errorData.error || `Error en la generación de la imagen: ${response.status} ${response.statusText}`;
                throw new Error(errorMessage);
            }

            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('image')) {
                console.error('Invalid content type:', contentType);
                throw new Error('La respuesta del servidor no es una imagen válida');
            }

            const blob = await response.blob();
            const imageUrl = URL.createObjectURL(blob);

            generatedImage.src = imageUrl;
            generatedImage.style.opacity = '1';
            generatedImage.onload = () => {
                generatedImageContainer.style.display = 'block';
                generatedImageContainer.scrollIntoView({ behavior: 'smooth' });
            };
            generatedImage.onerror = () => {
                throw new Error('Error al cargar la imagen generada');
            };
        } catch (error) {
            console.error('Error al generar la imagen:', error);
            if (response) {
                console.error('Response status:', response.status);
                console.error('Response headers:', Object.fromEntries(response.headers));
                console.error('Response type:', response.type);
            }
            showError(error.message || 'Error al generar la imagen. Por favor, intenta de nuevo.');
            resetImageContainer();
        } finally {
            setLoadingState(false);
        }
    });

    downloadBtn.addEventListener('click', () => {
        if (generatedImage.src) {
            downloadImage(generatedImage.src);
        }
    });

    saveBtn.addEventListener('click', async () => {
        if (generatedImage.src) {
            await saveImageToHistory(generatedImage.src, promptInput.value);
            showError('Imagen guardada en tu historial');
        }
    });

    function downloadImage(url) {
        const a = document.createElement('a');
        a.href = url;
        a.download = 'imagen-generada.jpg';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    }

    promptInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !generateBtn.disabled) {
            generateBtn.click();
        }
    });
}

// Guardar imagen en el historial
async function saveImageToHistory(imageUrl, prompt) {
    try {
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) return;

        // Convertir URL a Blob y luego a Base64
        const response = await fetch(imageUrl);
        const blob = await response.blob();

        const reader = new FileReader();
        reader.readAsDataURL(blob);

        reader.onloadend = async () => {
            const base64data = reader.result;

            // Guardar en Supabase
            const { data, error } = await supabaseClient
                .from('images')
                .insert([
                    {
                        user_id: user.id,
                        prompt: prompt,
                        image_data: base64data,
                        created_at: new Date()
                    }
                ]);

            if (error) {
                console.error('Error al guardar la imagen:', error);
                return;
            }

            // Recargar historial
            loadImageHistory();
        };
    } catch (error) {
        console.error('Error al procesar la imagen:', error);
    }
}

// Cargar historial de imágenes
async function loadImageHistory() {
    const historyGallery = document.getElementById('history-gallery');

    try {
        const { data: { user } } = await supabaseClient.auth.getUser();

        if (!user) return;

        // Obtener imágenes del usuario
        const { data, error } = await supabaseClient
            .from('images')
            .select('*')
            .eq('user_id', user.id)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error al cargar historial:', error);
            return;
        }

        // Mostrar imágenes
        historyGallery.innerHTML = '';

        if (data.length === 0) {
            historyGallery.innerHTML = '<p class="empty-state">No hay imágenes guardadas aún.</p>';
            return;
        }

        data.forEach(item => {
            const imageItem = document.createElement('div');
            imageItem.className = 'history-item';

            const img = document.createElement('img');
            img.src = item.image_data;
            img.alt = item.prompt;
            img.title = item.prompt;

            const actions = document.createElement('div');
            actions.className = 'history-actions';

            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'history-action-btn';
            downloadBtn.innerHTML = '↓';
            downloadBtn.title = 'Descargar';
            downloadBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                downloadHistoryImage(item.image_data);
            });

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'history-action-btn';
            deleteBtn.innerHTML = '×';
            deleteBtn.title = 'Eliminar';
            deleteBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                deleteHistoryImage(item.id);
            });

            actions.appendChild(downloadBtn);
            actions.appendChild(deleteBtn);

            imageItem.appendChild(img);
            imageItem.appendChild(actions);

            historyGallery.appendChild(imageItem);
        });
    } catch (error) {
        console.error('Error al procesar historial:', error);
    }
}

// Descargar imagen del historial
function downloadHistoryImage(imageData) {
    const a = document.createElement('a');
    a.href = imageData;
    a.download = 'imagen-guardada.jpg';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
}

// Eliminar imagen del historial
async function deleteHistoryImage(imageId) {
    try {
        const { error } = await supabaseClient
            .from('images')
            .delete()
            .eq('id', imageId);

        if (error) {
            console.error('Error al eliminar imagen:', error);
            return;
        }

        // Recargar historial
        loadImageHistory();
    } catch (error) {
        console.error('Error al eliminar imagen:', error);
    }
}

// Cerrar sesión
async function handleLogout() {
    try {
        // Mostrar mensaje de carga
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.disabled = true;
            logoutBtn.textContent = 'Cerrando sesión...';
        }

        // Limpiar datos locales
        localStorage.removeItem('userEmail');
        localStorage.removeItem('userName');

        // Cerrar sesión en Supabase
        const { error } = await supabaseClient.auth.signOut();

        if (error) throw error;

        console.log('Sesión cerrada exitosamente');

        // Redirigir a la página principal
        window.location.href = 'index.html';
    } catch (error) {
        console.error('Error al cerrar sesión:', error);

        // Restaurar botón en caso de error
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.disabled = false;
            logoutBtn.textContent = 'Cerrar Sesión';
        }

        // Mostrar alerta
        alert('Error al cerrar sesión. Por favor, intenta de nuevo.');
    }
}
