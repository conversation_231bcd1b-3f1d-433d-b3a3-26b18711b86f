/* Estilos para la sección del generador en la página principal */

/* Variables */
:root {
    --primary-color: #6e8efb;
    --secondary-color: #a777e3;
    --accent-color: #a855f7;
    --background-dark: #13111C;
    --background-light: #1E1B2E;
    --text-color: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.7);
    --border-color: rgba(168, 85, 247, 0.2);
    --card-bg: rgba(30, 27, 46, 0.7);
    --card-border: rgba(168, 85, 247, 0.2);
    --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --card-radius: 16px;
    --transition-speed: 0.3s;
}

/* Contenedor del generador */
.generator-section {
    padding: 2rem 0 4rem;
    max-width: 1200px;
    margin: 0 auto;
}

.generator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin: 0 auto;
}

/* Tarjetas */
.prompt-card, .result-card {
    background: var(--card-bg);
    border-radius: var(--card-radius);
    border: 1px solid var(--card-border);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-header {
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
}

.card-header h3 i {
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Formulario */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 500;
}

.prompt-textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-color);
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
    transition: all var(--transition-speed) ease;
}

.prompt-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(110, 142, 251, 0.2);
}

/* Sugerencias */
.prompt-suggestions {
    margin-bottom: 1.5rem;
}

.prompt-suggestions h4 {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.8rem;
    color: var(--text-muted);
}

.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-chip {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.suggestion-chip:hover {
    background: rgba(110, 142, 251, 0.2);
}

/* Botones de acción */
.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: auto;
}

.generate-btn {
    padding: 0.8rem 1.5rem;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
    color: white;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed) ease;
    border: none;
    cursor: pointer;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.generate-btn:active {
    transform: translateY(0);
}

/* Contenedor de resultado */
.result-container {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    background: rgba(0, 0, 0, 0.2);
    margin-bottom: 1.5rem;
}

.empty-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    padding: 2rem;
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.result-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Indicador de carga */
.loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 27, 46, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.spinner-container {
    margin-bottom: 1rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Acciones de resultado */
.result-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.action-btn {
    padding: 0.8rem 1.2rem;
    background: rgba(255, 255, 255, 0.05);
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed) ease;
    color: var(--text-color);
    cursor: pointer;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Responsive */
@media (max-width: 1024px) {
    .generator-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .generator-section {
        padding: 1rem 1rem 3rem;
    }
    
    .suggestion-chips {
        justify-content: center;
    }
    
    .form-actions {
        justify-content: center;
    }
    
    .result-actions {
        flex-wrap: wrap;
    }
}

@media (max-width: 480px) {
    .card-header h3 {
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .form-group label {
        font-size: 0.9rem;
    }
    
    .prompt-textarea {
        min-height: 100px;
        padding: 0.8rem;
        font-size: 0.9rem;
    }
    
    .generate-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
        width: 100%;
    }
    
    .action-btn {
        padding: 0.7rem 1rem;
        font-size: 0.9rem;
        flex: 1;
        justify-content: center;
    }
}
