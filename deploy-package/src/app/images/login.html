<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar <PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON> Images</title>
    <link rel="icon" type="image/svg+xml" href="images/logo.svg">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="responsive.css">
    <style>
        .success-message {
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.3s ease-in-out;
        }
        .error-message {
            background-color: #f44336;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.3s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        /* Estilos mejorados para el formulario */
        .auth-card {
            width: 450px; /* Aumentado de 380px (valor por defecto) */
            max-width: 90%;
            padding: 40px; /* Aumentado de 30px */
        }
        .form-group {
            margin-bottom: 25px; /* Más espacio entre campos */
        }
        .form-group input {
            padding: 15px; /* Campos más altos */
            font-size: 16px; /* Texto más grande */
        }
        .auth-button {
            padding: 15px 0; /* Botón más alto */
            font-size: 18px; /* Texto del botón más grande */
            margin-top: 10px;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            border: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .auth-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, #5d7df9, #9665d8);
        }
        .auth-button:active {
            transform: translateY(1px);
        }
        .auth-title {
            font-size: 28px; /* Título más grande */
            margin-bottom: 30px; /* Más espacio debajo del título */
        }

        /* Estilos para hacer la página responsive */
        @media (max-width: 768px) {
            .auth-container {
                padding: 10px;
            }
            .auth-card {
                padding: 25px;
            }
            .auth-title {
                font-size: 24px;
                margin-bottom: 20px;
            }
            .form-group input {
                padding: 12px;
                font-size: 15px;
            }
            .auth-button {
                padding: 12px 0;
                font-size: 16px;
            }
            .auth-header h1 {
                font-size: 24px;
            }
            .auth-logo {
                width: 40px;
                height: 40px;
            }
        }

        @media (max-width: 480px) {
            .auth-card {
                padding: 20px;
            }
            .auth-title {
                font-size: 22px;
                margin-bottom: 15px;
            }
            .form-group {
                margin-bottom: 15px;
            }
            .form-group input {
                padding: 10px;
                font-size: 14px;
            }
            .auth-button {
                padding: 10px 0;
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <img src="images/logo.svg" alt="Flasti AI Logo" class="auth-logo">
                <h1>Flasti <span class="ai-text">Images</span></h1>
            </div>

            <h2 class="auth-title">Bienvenido</h2>

            <div id="error-message" class="error-message" style="display: none;"></div>

            <form id="login-form" onsubmit="return false;">
                <div class="form-group">
                    <label for="email">Correo Electrónico</label>
                    <input type="email" id="email" name="email" placeholder="Ingresa tu correo electrónico" required>
                </div>

                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <input type="password" id="password" name="password" placeholder="Ingresa tu contraseña" required>
                </div>

                <button type="submit" id="login-button" class="auth-button">Iniciar Sesión</button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Verificar que Supabase se haya cargado correctamente
        window.addEventListener('load', function() {
            if (typeof supabase === 'undefined') {
                console.error('Error: Supabase no se ha cargado correctamente');
                alert('Error al cargar los recursos necesarios. Por favor, recarga la página.');
            } else {
                console.log('Supabase cargado correctamente');
            }
        });
    </script>
    <script src="auth.js"></script>
</body>
</html>
