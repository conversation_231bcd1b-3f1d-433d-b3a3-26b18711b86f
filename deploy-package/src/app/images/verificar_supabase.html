<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificar Supabase</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
        }
        .info {
            background-color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .log-container {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .error {
            color: #d9534f;
        }
        .success {
            color: #5cb85c;
        }
        .info-log {
            color: #5bc0de;
        }
    </style>
</head>
<body>
    <h1>Verificación de Supabase</h1>
    
    <div class="info">
        <p>Esta página verifica la configuración de Supabase y muestra información detallada sobre cualquier error.</p>
        <p>Abre la consola del navegador (F12) para ver los resultados completos.</p>
    </div>
    
    <h2>Resultados:</h2>
    <div id="log-container" class="log-container">
        <div class="log-entry info-log">Iniciando verificación de Supabase...</div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Función para agregar entradas al log
        function addLogEntry(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = message;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Sobrescribir console.log, console.error, etc.
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleInfo = console.info;
        
        console.log = function() {
            originalConsoleLog.apply(console, arguments);
            addLogEntry(Array.from(arguments).join(' '), 'success');
        };
        
        console.error = function() {
            originalConsoleError.apply(console, arguments);
            addLogEntry(Array.from(arguments).join(' '), 'error');
        };
        
        console.info = function() {
            originalConsoleInfo.apply(console, arguments);
            addLogEntry(Array.from(arguments).join(' '), 'info-log');
        };
    </script>
    <script src="verificar_supabase.js"></script>
</body>
</html>
