<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contacto - Flasti Images</title>
    <link rel="icon" type="image/svg+xml" href="images/logo.svg">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="responsive.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #fff;
            background: linear-gradient(135deg, #13111C 0%, #1E1B2E 100%);
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #fff;
        }

        .logo img {
            height: 40px;
            margin-right: 10px;
        }

        .logo h1 {
            font-size: 24px;
            margin: 0;
        }

        .ai-text {
            color: #6e8efb;
        }

        .content {
            background-color: rgba(30, 27, 46, 0.5);
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-top: 80px;
        }

        h1 {
            color: #fff;
            margin-top: 0;
            margin-bottom: 30px;
            font-size: 32px;
            text-align: center;
        }

        .contact-info {
            margin-bottom: 40px;
            text-align: center;
        }

        .contact-info p {
            margin-bottom: 20px;
            font-size: 18px;
        }

        .contact-email {
            color: #a855f7;
            font-weight: bold;
            text-decoration: none;
        }

        .contact-email:hover {
            text-decoration: underline;
        }

        .contact-form {
            max-width: 600px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
        }

        input, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            font-size: 16px;
        }

        textarea {
            min-height: 150px;
            resize: vertical;
        }

        button {
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .back-button {
            display: inline-block;
            margin-top: 30px;
            padding: 12px 25px;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .content {
                padding: 20px;
                margin-top: 60px;
            }

            h1 {
                font-size: 28px;
            }

            .contact-info p {
                font-size: 16px;
            }
        }

        /* Estilos para la navegación */
        nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(19, 17, 28, 0.9);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 15px 0;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
        }

        .nav-logo-img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
        }

        .nav-logo h1 {
            font-size: 20px;
            margin: 0;
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html" class="logo">
                    <img src="images/logo.svg" alt="Flasti Images Logo" class="nav-logo-img">
                    <h1>Flasti <span class="ai-text">Images</span></h1>
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="content">
            <h1>Contacto</h1>

            <div class="contact-info">
                <p>¿Tienes alguna pregunta o comentario? Estamos aquí para ayudarte.</p>
                <p>Puedes contactarnos directamente a: <a href="mailto:<EMAIL>" class="contact-email"><EMAIL></a></p>
                <p>O utiliza el formulario a continuación:</p>
            </div>

            <div class="contact-form">
                <form id="contact-form">
                    <div class="form-group">
                        <label for="name">Nombre</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Correo Electrónico</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="subject">Asunto</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>

                    <div class="form-group">
                        <label for="message">Mensaje</label>
                        <textarea id="message" name="message" required></textarea>
                    </div>

                    <button type="submit">Enviar Mensaje</button>
                </form>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="index.html" class="back-button">Volver al Inicio</a>
            </div>
        </div>

        <footer>
            <p>© 2025 Flasti Images | Todos los derechos reservados.</p>
        </footer>
    </div>

    <script>
        document.getElementById('contact-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Gracias por tu mensaje. Te responderemos lo antes posible.');
            this.reset();
        });
    </script>
</body>
</html>
