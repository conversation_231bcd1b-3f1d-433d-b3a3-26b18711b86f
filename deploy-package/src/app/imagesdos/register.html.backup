<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro - Flasti AI</title>
    <link rel="icon" type="image/svg+xml" href="images/logo.svg">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <img src="images/logo.svg" alt="Flasti AI Logo" class="auth-logo">
                <h1>Flasti <span class="ai-text">AI</span></h1>
            </div>
            <h2>Crear Cuenta</h2>
            <div id="error-message" class="error-message" style="display: none;"></div>
            <form id="register-form" onsubmit="return false;">
                <div class="form-group">
                    <label for="name">Nombre Completo</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="email">Correo Electrónico</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <input type="password" id="password" name="password" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="confirm-password">Confirmar Contraseña</label>
                    <input type="password" id="confirm-password" name="confirm-password" required minlength="6">
                </div>
                <div class="form-group checkbox-group">
                    <input type="checkbox" id="terms" name="terms" required>
                    <label for="terms">Acepto los <a href="#" class="terms-link">términos y condiciones</a></label>
                </div>
                <button type="submit" class="auth-button">Crear Cuenta</button>
            </form>
            <p class="auth-link">¿Ya tienes una cuenta? <a href="login.html">Inicia Sesión</a></p>
            <p class="auth-link"><a href="index.html">Volver al inicio</a></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Verificar que Supabase se haya cargado correctamente
        window.addEventListener('load', function() {
            if (typeof supabase === 'undefined') {
                console.error('Error: Supabase no se ha cargado correctamente');
                alert('Error al cargar los recursos necesarios. Por favor, recarga la página.');
            } else {
                console.log('Supabase cargado correctamente');
            }
        });
    </script>
    <script src="auth_functions.js"></script>
    <script src="auth.js"></script>
</body>
</html>
