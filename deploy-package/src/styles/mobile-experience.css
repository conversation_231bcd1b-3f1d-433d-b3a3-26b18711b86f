/* Mobile Experience Utilities
   Este archivo contiene utilidades CSS para mejorar la experiencia móvil
   y hacer que la aplicación se sienta más como una app nativa.
*/

/* Variables para espaciado consistente en móvil */
:root {
  /* Variables para áreas seguras (notch, etc.) */
  --safe-area-inset-top: env(safe-area-inset-top, 0px);
  --safe-area-inset-right: env(safe-area-inset-right, 0px);
  --safe-area-inset-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-inset-left: env(safe-area-inset-left, 0px);
  --mobile-padding-x: 16px;
  --mobile-padding-y: 16px;
  --mobile-element-spacing: 12px;
  --mobile-border-radius: 12px;
  --mobile-button-height: 48px;
  --mobile-input-height: 48px;
  --mobile-touch-target: 44px; /* <PERSON><PERSON><PERSON> mínimo recomendado para elementos táctiles */
}

/* Clase para hacer que los elementos sean más fáciles de tocar en móvil */
.mobile-touch-friendly {
  min-height: var(--mobile-touch-target);
  min-width: var(--mobile-touch-target);
}

/* Mejora la experiencia de desplazamiento en dispositivos móviles */
.mobile-smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  overscroll-behavior-y: contain;
}

/* Evita el zoom no deseado en inputs en iOS */
@media (max-width: 767px) {
  input, select, textarea {
    font-size: 16px !important;
  }
}

/* Mejora la experiencia de toque para botones en móvil */
@media (max-width: 767px) {
  button,
  .button,
  [role="button"],
  a.button,
  .btn,
  input[type="button"],
  input[type="submit"] {
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
}

/* Ajusta el padding para contenedores en móvil */
@media (max-width: 767px) {
  .container-custom,
  .mobile-container {
    padding-left: var(--mobile-padding-x);
    padding-right: var(--mobile-padding-x);
  }

  .mobile-py {
    padding-top: var(--mobile-padding-y);
    padding-bottom: var(--mobile-padding-y);
  }
}

/* Mejora la experiencia de formularios en móvil */
@media (max-width: 767px) {
  .mobile-form-field {
    margin-bottom: 16px;
  }

  .mobile-form-field input,
  .mobile-form-field select,
  .mobile-form-field textarea {
    height: var(--mobile-input-height);
    border-radius: var(--mobile-border-radius);
    padding: 12px 16px;
  }

  .mobile-form-field button {
    height: var(--mobile-button-height);
    border-radius: var(--mobile-border-radius);
  }
}

/* Mejora la experiencia de tarjetas en móvil */
@media (max-width: 767px) {
  .mobile-card {
    border-radius: var(--mobile-border-radius);
    padding: var(--mobile-padding-y) var(--mobile-padding-x);
    margin-bottom: var(--mobile-element-spacing);
  }
}

/* Mejora la experiencia de navegación en móvil */
@media (max-width: 767px) {
  .mobile-nav-item {
    min-height: var(--mobile-touch-target);
    display: flex;
    align-items: center;
    padding: 12px 16px;
  }
}

/* Mejora la experiencia de botones flotantes en móvil */
.mobile-floating-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 100;
}

/* Clases para áreas seguras */
.safe-area-inset-top {
  padding-top: var(--safe-area-inset-top);
}

.safe-area-inset-right {
  padding-right: var(--safe-area-inset-right);
}

.safe-area-inset-bottom {
  padding-bottom: var(--safe-area-inset-bottom);
}

.safe-area-inset-left {
  padding-left: var(--safe-area-inset-left);
}

.safe-area-inset-all {
  padding-top: var(--safe-area-inset-top);
  padding-right: var(--safe-area-inset-right);
  padding-bottom: var(--safe-area-inset-bottom);
  padding-left: var(--safe-area-inset-left);
}

/* Mejora la experiencia de modales en móvil */
@media (max-width: 767px) {
  .mobile-modal {
    border-radius: var(--mobile-border-radius) var(--mobile-border-radius) 0 0;
    margin-bottom: 0;
    max-height: 90vh;
  }

  .mobile-modal-sheet {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: var(--mobile-border-radius) var(--mobile-border-radius) 0 0;
    padding: var(--mobile-padding-y) var(--mobile-padding-x);
    z-index: 50;
  }
}

/* Mejora la experiencia de listas en móvil */
@media (max-width: 767px) {
  .mobile-list-item {
    padding: 14px var(--mobile-padding-x);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mobile-list-item:last-child {
    border-bottom: none;
  }
}

/* Animaciones para mejorar la experiencia móvil */
.mobile-fade-in {
  animation: mobileFadeIn 0.3s ease-in-out;
}

.mobile-slide-up {
  animation: mobileSlideUp 0.3s ease-in-out;
}

@keyframes mobileFadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes mobileSlideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Mejora la experiencia de feedback táctil */
@media (max-width: 767px) {
  .mobile-touch-feedback {
    transition: transform 0.1s ease-in-out, opacity 0.1s ease-in-out;
  }

  .mobile-touch-feedback:active {
    transform: scale(0.97);
    opacity: 0.9;
  }
}

/* Mejora la experiencia de navegación por pestañas en móvil */
.mobile-tabs {
  display: flex;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  margin-bottom: var(--mobile-element-spacing);
}

.mobile-tabs::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Opera */
}

.mobile-tab {
  flex: 0 0 auto;
  padding: 12px 16px;
  white-space: nowrap;
  position: relative;
}

.mobile-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 2px;
  background-color: currentColor;
}
