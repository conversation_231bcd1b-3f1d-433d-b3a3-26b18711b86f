<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>¡Has recibido una comisión! - flasti</title>
    <style>
        /* Estilos generales */
        body {
            font-family: '<PERSON><PERSON><PERSON><PERSON>', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
            color: #1e1e2e;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .logo {
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.01em;
            color: #1e1e2e;
            margin: 0;
        }
        .logo-gradient {
            background: linear-gradient(90deg, #9333ea, #ec4899);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }
        .content {
            padding: 30px 20px;
        }
        .commission-box {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        .commission-amount {
            font-size: 36px;
            font-weight: bold;
            color: #9333ea;
            margin: 10px 0;
        }
        .commission-label {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 15px;
        }
        .transaction-details {
            margin: 30px 0;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .transaction-header {
            background-color: #f8fafc;
            padding: 15px;
            font-weight: 600;
            border-bottom: 1px solid #f0f0f0;
        }
        .transaction-body {
            padding: 15px;
        }
        .transaction-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .transaction-row:last-child {
            border-bottom: none;
        }
        .transaction-label {
            color: #6b7280;
        }
        .transaction-value {
            font-weight: 500;
        }
        .button {
            display: block;
            width: 200px;
            margin: 30px auto;
            padding: 15px 25px;
            background: linear-gradient(90deg, #9333ea, #ec4899);
            color: white;
            text-align: center;
            text-decoration: none;
            font-weight: 600;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #f0f0f0;
        }
        .social-links {
            margin: 15px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6b7280;
            text-decoration: none;
        }
        .help-text {
            margin-top: 30px;
            font-size: 14px;
            color: #6b7280;
        }
        
        /* Estilos responsivos */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100%;
                border-radius: 0;
            }
            .content {
                padding: 20px 15px;
            }
            .button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="logo"><span class="logo-gradient">flasti</span></h1>
        </div>
        
        <div class="content">
            <h2>¡Has recibido una comisión!</h2>
            
            <p>Hola {{ .Name }},</p>
            
            <p>¡Buenas noticias! Has recibido una nueva comisión por una venta realizada a través de tu enlace de afiliado.</p>
            
            <div class="commission-box">
                <div class="commission-label">Comisión recibida</div>
                <div class="commission-amount">${{ .Amount }} USD</div>
                <div>Tu saldo actual: ${{ .Balance }} USD</div>
            </div>
            
            <div class="transaction-details">
                <div class="transaction-header">
                    Detalles de la transacción
                </div>
                <div class="transaction-body">
                    <div class="transaction-row">
                        <div class="transaction-label">ID de transacción</div>
                        <div class="transaction-value">{{ .TransactionId }}</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Fecha</div>
                        <div class="transaction-value">{{ .Date }}</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Producto</div>
                        <div class="transaction-value">{{ .ProductName }}</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Precio de venta</div>
                        <div class="transaction-value">${{ .SaleAmount }} USD</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Porcentaje de comisión</div>
                        <div class="transaction-value">{{ .CommissionRate }}%</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Comisión ganada</div>
                        <div class="transaction-value">${{ .Amount }} USD</div>
                    </div>
                </div>
            </div>
            
            <p>Esta comisión ya ha sido añadida a tu saldo disponible. Puedes retirar tus ganancias cuando alcances el monto mínimo de retiro.</p>
            
            <a href="https://flasti.com/dashboard" class="button">Ver mi panel</a>
            
            <div class="help-text">
                <p>¿Quieres ganar más? Sigue compartiendo tus enlaces de afiliado y aprovecha nuestros recursos de marketing para maximizar tus ganancias.</p>
                <p>Si tienes alguna pregunta sobre esta transacción, no dudes en contactar a nuestro equipo de soporte.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 flasti. Todos los derechos reservados.</p>
            <div class="social-links">
                <a href="https://twitter.com/flasti">Twitter</a> • 
                <a href="https://instagram.com/flasti">Instagram</a> • 
                <a href="https://facebook.com/flasti">Facebook</a>
            </div>
            <p>flasti - Ganancia colectiva</p>
        </div>
    </div>
</body>
</html>
