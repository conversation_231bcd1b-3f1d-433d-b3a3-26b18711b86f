<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retiro procesado con éxito - flasti</title>
    <style>
        /* Estilos generales */
        body {
            font-family: '<PERSON><PERSON><PERSON><PERSON>', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
            color: #1e1e2e;
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .logo {
            font-size: 28px;
            font-weight: 600;
            letter-spacing: -0.01em;
            color: #1e1e2e;
            margin: 0;
        }
        .logo-gradient {
            background: linear-gradient(90deg, #9333ea, #ec4899);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }
        .content {
            padding: 30px 20px;
        }
        .withdrawal-box {
            background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(236, 72, 153, 0.1) 100%);
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        .withdrawal-amount {
            font-size: 36px;
            font-weight: bold;
            color: #9333ea;
            margin: 10px 0;
        }
        .withdrawal-label {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 15px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            background-color: #10b981;
            color: white;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin-top: 10px;
        }
        .transaction-details {
            margin: 30px 0;
            border: 1px solid #f0f0f0;
            border-radius: 8px;
            overflow: hidden;
        }
        .transaction-header {
            background-color: #f8fafc;
            padding: 15px;
            font-weight: 600;
            border-bottom: 1px solid #f0f0f0;
        }
        .transaction-body {
            padding: 15px;
        }
        .transaction-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .transaction-row:last-child {
            border-bottom: none;
        }
        .transaction-label {
            color: #6b7280;
        }
        .transaction-value {
            font-weight: 500;
        }
        .button {
            display: block;
            width: 200px;
            margin: 30px auto;
            padding: 15px 25px;
            background: linear-gradient(90deg, #9333ea, #ec4899);
            color: white;
            text-align: center;
            text-decoration: none;
            font-weight: 600;
            border-radius: 30px;
            transition: all 0.3s ease;
        }
        .button:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        .footer {
            text-align: center;
            padding: 20px;
            color: #6b7280;
            font-size: 14px;
            border-top: 1px solid #f0f0f0;
        }
        .social-links {
            margin: 15px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #6b7280;
            text-decoration: none;
        }
        .help-text {
            margin-top: 30px;
            font-size: 14px;
            color: #6b7280;
        }
        
        /* Estilos responsivos */
        @media only screen and (max-width: 600px) {
            .container {
                width: 100%;
                border-radius: 0;
            }
            .content {
                padding: 20px 15px;
            }
            .button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="logo"><span class="logo-gradient">flasti</span></h1>
        </div>
        
        <div class="content">
            <h2>Tu retiro ha sido procesado</h2>
            
            <p>Hola {{ .Name }},</p>
            
            <p>Nos complace informarte que tu solicitud de retiro ha sido procesada con éxito y los fondos han sido enviados a tu método de pago seleccionado.</p>
            
            <div class="withdrawal-box">
                <div class="withdrawal-label">Monto retirado</div>
                <div class="withdrawal-amount">${{ .Amount }} USD</div>
                <div>Tu saldo actual: ${{ .Balance }} USD</div>
                <div class="status-badge">Procesado</div>
            </div>
            
            <div class="transaction-details">
                <div class="transaction-header">
                    Detalles del retiro
                </div>
                <div class="transaction-body">
                    <div class="transaction-row">
                        <div class="transaction-label">ID de transacción</div>
                        <div class="transaction-value">{{ .TransactionId }}</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Fecha de solicitud</div>
                        <div class="transaction-value">{{ .RequestDate }}</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Fecha de procesamiento</div>
                        <div class="transaction-value">{{ .ProcessDate }}</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Método de pago</div>
                        <div class="transaction-value">{{ .PaymentMethod }}</div>
                    </div>
                    <div class="transaction-row">
                        <div class="transaction-label">Monto</div>
                        <div class="transaction-value">${{ .Amount }} USD</div>
                    </div>
                </div>
            </div>
            
            <p>Dependiendo de tu método de pago, los fondos pueden tardar entre 1 y 5 días hábiles en aparecer en tu cuenta.</p>
            
            <a href="https://flasti.com/dashboard/withdrawals" class="button">Ver mis retiros</a>
            
            <div class="help-text">
                <p>Si tienes alguna pregunta sobre este retiro o si no recibes los fondos en el tiempo estimado, no dudes en contactar a nuestro equipo de soporte.</p>
                <p>Gracias por ser parte de la comunidad flasti.</p>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 flasti. Todos los derechos reservados.</p>
            <div class="social-links">
                <a href="https://twitter.com/flasti">Twitter</a> • 
                <a href="https://instagram.com/flasti">Instagram</a> • 
                <a href="https://facebook.com/flasti">Facebook</a>
            </div>
            <p>flasti - Ganancia colectiva</p>
        </div>
    </div>
</body>
</html>
