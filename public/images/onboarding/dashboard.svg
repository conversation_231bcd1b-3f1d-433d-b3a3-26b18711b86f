<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="dashboardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#3b82f6" />
      <stop offset="50%" stop-color="#8b5cf6" />
      <stop offset="100%" stop-color="#ec4899" />
    </linearGradient>
    <filter id="shadow" x="-10%" y="-10%" width="120%" height="120%">
      <feDropShadow dx="0" dy="4" stdDeviation="6" flood-color="#3b82f6" flood-opacity="0.3"/>
    </filter>
    <clipPath id="screenClip">
      <rect x="60" y="70" width="180" height="140" rx="8"/>
    </clipPath>
  </defs>

  <!-- Background -->
  <circle cx="150" cy="150" r="150" fill="#f8fafc" opacity="0.05"/>
  <circle cx="150" cy="150" r="120" fill="url(#dashboardGradient)" opacity="0.1"/>

  <!-- Dashboard Monitor -->
  <rect x="60" y="70" width="180" height="140" rx="8" fill="#1e293b" filter="url(#shadow)"/>
  <rect x="60" y="70" width="180" height="20" rx="8" fill="#0f172a"/>

  <!-- Monitor Stand -->
  <path d="M140,210 L160,210 L165,220 L135,220 Z" fill="#334155"/>
  <rect x="130" y="220" width="40" height="10" rx="2" fill="#475569"/>

  <!-- Screen Content -->
  <g clip-path="url(#screenClip)">
    <!-- Header -->
    <rect x="60" y="70" width="180" height="30" fill="#1e293b"/>
    <circle cx="75" cy="85" r="8" fill="url(#dashboardGradient)" opacity="0.8"/>
    <rect x="90" y="80" width="60" height="10" rx="2" fill="#64748b" opacity="0.6"/>
    <rect x="200" y="80" width="30" height="10" rx="2" fill="#64748b" opacity="0.6"/>

    <!-- Sidebar -->
    <rect x="60" y="100" width="40" height="110" fill="#0f172a"/>
    <circle cx="80" cy="120" r="6" fill="#3b82f6" opacity="0.8"/>
    <circle cx="80" cy="145" r="6" fill="#8b5cf6" opacity="0.8"/>
    <circle cx="80" cy="170" r="6" fill="#ec4899" opacity="0.8"/>
    <circle cx="80" cy="195" r="6" fill="#f59e0b" opacity="0.8"/>

    <!-- Main Content -->
    <!-- Stats Cards -->
    <rect x="110" y="110" width="120" height="40" rx="4" fill="#334155" opacity="0.8"/>
    <rect x="120" y="120" width="40" height="8" rx="2" fill="#94a3b8" opacity="0.6"/>
    <rect x="120" y="132" width="60" height="10" rx="2" fill="#f8fafc" opacity="0.8"/>

    <!-- Chart -->
    <rect x="110" y="160" width="120" height="70" rx="4" fill="#334155" opacity="0.8"/>
    <rect x="120" y="170" width="40" height="8" rx="2" fill="#94a3b8" opacity="0.6"/>

    <!-- Chart Lines -->
    <path d="M120,200 L130,190 L140,195 L150,180 L160,185 L170,175 L180,165 L190,175 L200,170"
          stroke="#3b82f6" stroke-width="2" stroke-linecap="round" opacity="0.8"/>
    <path d="M120,210 L130,205 L140,210 L150,200 L160,205 L170,195 L180,200 L190,190 L200,195"
          stroke="#ec4899" stroke-width="2" stroke-linecap="round" opacity="0.8"/>

    <!-- Chart Points -->
    <circle cx="130" cy="190" r="3" fill="#3b82f6"/>
    <circle cx="150" cy="180" r="3" fill="#3b82f6"/>
    <circle cx="170" cy="175" r="3" fill="#3b82f6"/>
    <circle cx="190" cy="175" r="3" fill="#3b82f6"/>

    <circle cx="130" cy="205" r="3" fill="#ec4899"/>
    <circle cx="150" cy="200" r="3" fill="#ec4899"/>
    <circle cx="170" cy="195" r="3" fill="#ec4899"/>
    <circle cx="190" cy="190" r="3" fill="#ec4899"/>
  </g>

  <!-- Decorative Elements -->
  <circle cx="60" cy="60" r="10" fill="#3b82f6" opacity="0.3"/>
  <circle cx="240" cy="60" r="10" fill="#ec4899" opacity="0.3"/>
  <circle cx="60" cy="240" r="10" fill="#8b5cf6" opacity="0.3"/>
  <circle cx="240" cy="240" r="10" fill="#f59e0b" opacity="0.3"/>

  <!-- Panel Personal Text -->
  <text x="150" y="250" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#dashboardGradient)" text-anchor="middle" filter="url(#shadow)">Panel Personal</text>
</svg>
