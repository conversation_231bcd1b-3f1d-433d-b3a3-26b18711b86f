/* Estilos para el tema de casino */
:root {
  /* Colores principales - Modo oscuro (por defecto) */
  --casino-primary: #8b5cf6;
  --casino-secondary: #a855f7;
  --casino-accent: #ec4899;
  --casino-background: #0f0f12;
  --casino-card-bg: #1a1a22;
  --casino-text: #ffffff;
  --casino-text-muted: rgba(255, 255, 255, 0.7);
  --casino-border: rgba(139, 92, 246, 0.2);

  /* Tamaños */
  --sidebar-width: 240px;
  --sidebar-expanded-width: 240px;
  --header-height: 100px;
  --card-radius: 16px;

  /* Colores de tarjetas */
  --casino-card-1: #ec4899;
  --casino-card-2: #9333ea;
  --casino-card-3: #0ea5e9;
  --casino-card-4: #22c55e;
  --casino-card-5: #f59e0b;
  --casino-card-6: #8b5cf6;
}

/* Layout principal */
.casino-layout {
  display: grid;
  grid-template-columns: var(--sidebar-width) 1fr;
  grid-template-rows: var(--header-height) 1fr;
  grid-template-areas:
    "sidebar header"
    "sidebar main";
  min-height: 100vh;
  background-color: var(--casino-background);
  background-image: none;
  color: var(--casino-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Eliminado el estilo para modo claro */

/* Ajustar layout para móvil */
@media (max-width: 767px) {
  .casino-layout {
    grid-template-columns: 0px 1fr;
  }

  /* Asegurar que el botón de menú sea visible */
  .header-left {
    z-index: 100;
    padding-left: 0.5rem;
  }

  /* Estilo para el botón de menú en móvil */
  .mobile-menu-button {
    display: flex !important;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-right: 0.5rem;
  }
}

/* Sidebar */
.casino-sidebar {
  grid-area: sidebar;
  background-color: #1E1B2E;
  border-right: 1px solid rgba(255, 255, 255, 0.05);
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: width 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
  overflow: hidden;
  z-index: 50;
}

/* En escritorio, siempre alineado a la izquierda (excepto el logo) */
@media (min-width: 768px) {
  .casino-sidebar {
    align-items: flex-start;
  }

  .sidebar-logo {
    justify-content: flex-start;
    padding-left: 2.5rem; /* Desplaza más hacia la izquierda */
  }
}

/* Eliminados los estilos para modo claro */

.casino-sidebar.expanded {
  width: var(--sidebar-expanded-width);
  align-items: flex-start;
}

.sidebar-logo {
  margin-bottom: 2rem;
  padding: 0;
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 1.5rem;
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  width: 100%;
  padding-top: 0.5rem;
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  padding: 0.65rem 1.2rem;
  border-radius: 10px;
  margin: 0.2rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.sidebar-nav-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.sidebar-nav-item:hover .sidebar-nav-item-text {
  color: rgba(255, 255, 255, 0.95);
}

.sidebar-nav-item:hover .sidebar-nav-item-icon {
  transform: scale(1.05);
}

.sidebar-nav-item.active {
  background-color: rgba(110, 142, 251, 0.15);
  position: relative;
}

.sidebar-nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 3px;
  background: linear-gradient(to bottom, var(--casino-primary), var(--casino-secondary));
  border-radius: 0 4px 4px 0;
}

.sidebar-nav-item.active .sidebar-nav-item-text {
  color: white;
  font-weight: 500;
}

.sidebar-nav-item.active .sidebar-nav-item-icon {
  background-color: rgba(110, 142, 251, 0.3) !important;
}

.sidebar-nav-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 0.9rem;
  transition: all 0.2s ease;
}

.sidebar-nav-item-text {
  font-weight: 400;
  font-size: 0.9rem;
  letter-spacing: 0.02em;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.75);
  text-transform: capitalize;
  font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Siempre mostrar el texto en escritorio */
@media (min-width: 768px) {
  .sidebar-nav-item-text {
    opacity: 1;
  }
}

/* En móvil, solo mostrar cuando está expandido */
@media (max-width: 767px) {
  .casino-sidebar {
    width: 80px;
  }

  .casino-sidebar.expanded {
    width: var(--sidebar-expanded-width);
    height: 100vh;
    overflow-y: auto;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 100;
  }

  .casino-sidebar.expanded .sidebar-nav-item-text {
    opacity: 1;
  }
}

/* Header */
.casino-header {
  grid-area: header;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 3rem;
  background-color: #1E1B2E;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  z-index: 40;
  transition: background-color 0.3s ease, border-color 0.3s ease;
  height: var(--header-height);
}

/* Ajustar padding del header en móvil */
@media (max-width: 767px) {
  .casino-header {
    padding: 0 1rem;
  }
}

/* Eliminados los estilos para modo claro */

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 2.5rem;
}

.user-balance {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background-color: rgba(20, 20, 25, 0.95);
  padding: 0.4rem 0.8rem;
  border-radius: 8px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.05);
  margin: 0.75rem 0.5rem;
  transition: all 0.2s ease;
  height: auto;
  position: relative;
  min-width: 140px;
}

/* Eliminados los estilos para modo claro */

.user-balance:hover {
  border-color: rgba(255, 255, 255, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.user-balance-amount {
  font-weight: 600;
  letter-spacing: 0.01em;
  font-size: 0.9rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.user-balance-usd {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  margin-top: -2px;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--casino-primary);
}

/* Main content */
.casino-main {
  grid-area: main;
  padding: 1.5rem;
  overflow-y: auto;
}

/* Eliminados los estilos para modo claro */

/* Banner principal */
.main-banner {
  background: #1E1B2E;
  border-radius: var(--card-radius);
  padding: 2rem;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 200px;
}

.banner-content {
  max-width: 60%;
  z-index: 1;
}

.banner-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: #fff;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.banner-subtitle {
  font-size: 1rem;
  color: var(--casino-text-muted);
  margin-bottom: 1.5rem;
}

.banner-action {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--casino-primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.banner-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.banner-image {
  position: absolute;
  right: 2rem;
  bottom: 0;
  height: 90%;
  z-index: 0;
}

/* Carrusel de aplicaciones */
.apps-carousel {
  margin-bottom: 1.5rem;
  position: relative;
}

.apps-carousel::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 60px;
  background: none;
  pointer-events: none;
  z-index: 1;
  bottom: 0;
  top: 60px; /* Para que no afecte a los botones de navegación */
}

/* Eliminados los estilos para modo claro */

.carousel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.carousel-title-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.carousel-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.carousel-nav {
  display: flex;
  gap: 0.5rem;
}

/* Por defecto, las flechas móviles están ocultas en escritorio */
.carousel-nav-mobile {
  display: none;
}

/* En móviles, mostrar las flechas junto al título y ocultar las de escritorio */
@media (max-width: 768px) {
  .carousel-nav-desktop {
    display: none;
  }

  .carousel-nav-mobile {
    display: flex;
  }

  .carousel-title-container {
    flex: 1;
  }

  .carousel-title {
    font-size: 1.1rem;
  }

  .carousel-nav-mobile .carousel-nav-button {
    width: 30px;
    height: 30px;
    background-color: rgba(110, 142, 251, 0.25);
    color: white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
  }

  .carousel-nav-mobile {
    margin-left: auto;
    padding-left: 0.5rem;
  }

  /* Ajustar el indicador de desplazamiento en móviles */
  .apps-carousel::after {
    width: 40px;
    top: 50px; /* Ajuste para móviles */
  }
}

.carousel-nav-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  z-index: 5;
  position: relative;
}

:root.light .carousel-nav-button {
  background-color: rgba(139, 92, 246, 0.3);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.carousel-nav-button:hover {
  background-color: rgba(110, 142, 251, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

:root.light .carousel-nav-button:hover {
  background-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.carousel-nav-button:active {
  transform: translateY(0);
  background-color: rgba(110, 142, 251, 0.6);
}

.carousel-items {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 0.5rem 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch; /* Para mejor desplazamiento en iOS */
  user-select: none; /* Evita selección de texto al arrastrar */
}

.carousel-items::-webkit-scrollbar {
  display: none;
}

.app-card {
  min-width: 160px;
  background-color: var(--casino-card-bg);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  will-change: transform; /* Optimiza las animaciones */
  transform: translateZ(0); /* Activa la aceleración por hardware */
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.app-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border-color: var(--casino-primary);
}

.app-card:hover .active-app-image {
  filter: brightness(1.1);
  background-color: rgba(255, 255, 255, 0.05);
}

.app-card:hover .active-app-overlay {
  backdrop-filter: blur(0px);
  background: rgba(255, 255, 255, 0.05) !important;
}

.app-card:hover .active-app-logo {
  transform: scale(1.05);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
  background: rgba(255, 255, 255, 0.1);
}

/* Estilos para las tarjetas de próximamente */
.app-card-coming-soon {
  opacity: 0.95;
  cursor: default;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.app-card-coming-soon:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.coming-soon-image {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(30, 27, 46, 0.8), rgba(30, 27, 46, 0.95));
}

.coming-soon-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--casino-text-muted);
  margin-top: 0.25rem;
}

/* Estilo para redondear los bordes del logo en las tarjetas coming soon */
.app-logo-image {
  border-radius: 12px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
  background: rgba(255, 255, 255, 0.05);
  padding: 8px;
  backdrop-filter: blur(5px);
  object-fit: contain;
  max-width: 100%;
  height: auto;
}

.coming-soon-image:hover .app-logo-image {
  transform: scale(1.05);
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
  background: rgba(255, 255, 255, 0.1);
}

.coming-soon-label svg {
  color: #f59e0b;
}

.coming-soon-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  padding: 0.5rem 0;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  margin-top: 10px; /* Ajustar la posición del texto */
}

.coming-soon-stats {
  margin-top: 0.5rem;
  opacity: 0.6;
}

/* Estilo cuando se está arrastrando el carrusel */
.carousel-items.dragging {
  cursor: grabbing !important;
  scroll-behavior: auto; /* Desactiva el scroll suave durante el arrastre */
}

.carousel-items.dragging .app-card {
  pointer-events: none; /* Evita clics accidentales durante el arrastre */
  transform: scale(0.98); /* Efecto sutil de escala */
}

.app-card-image {
  height: 100px;
  background-size: cover;
  background-position: center;
}

.active-app-image {
  border-radius: 8px 8px 0 0;
  backdrop-filter: blur(5px);
  background-blend-mode: overlay;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  background-size: cover;
  background-position: center;
}

.active-app-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  backdrop-filter: blur(2px);
  transition: all 0.3s ease;
}

.active-app-logo {
  border-radius: 12px;
  object-fit: contain;
  background: rgba(255, 255, 255, 0.05);
  padding: 8px;
  backdrop-filter: blur(5px);
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
  max-width: 100%;
  height: auto;
}

.app-card-content {
  padding: 1rem;
}

.app-card-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

.app-card-stats {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--casino-text-muted);
}

/* Promociones destacadas */
.featured-promos {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.promo-card {
  background-color: var(--casino-card-bg);
  border-radius: 0.75rem;
  overflow: hidden;
  position: relative;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 1.5rem;
  background-size: cover;
  background-position: center;
  transition: all 0.3s ease;
  text-decoration: none;
  color: var(--casino-text);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.promo-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(0deg, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0.2) 100%);
  z-index: 0;
}

.promo-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
  border-color: var(--casino-primary);
}

.promo-card-content {
  position: relative;
  z-index: 1;
}

.promo-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.promo-card-description {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.promo-card-action {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  padding: 0.6rem 1.2rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.promo-card-action:hover {
  background-color: var(--casino-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-color: var(--casino-primary);
}

/* Estilos para los consejos de marketing */

.marketing-tips-container {
  background: linear-gradient(135deg, #1E1B2E, #2a2640);
  border-radius: 16px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

:root.light .marketing-tips-container {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(16, 185, 129, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.marketing-tips-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.marketing-tips-title svg {
  color: #22c55e; /* Verde para el icono de la bombilla */
}

.marketing-tips-content {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.25rem;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 1rem;
  border-radius: 0.75rem;
  border-left: 3px solid #22c55e;
  max-width: 100%;
  overflow: hidden;
}

:root.light .marketing-tips-content {
  background-color: rgba(255, 255, 255, 0.5);
  border-left: 3px solid #22c55e;
}

.marketing-tip-icon {
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 50%;
  background-color: rgba(34, 197, 94, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.marketing-tip-icon svg {
  color: #22c55e;
}

.marketing-tip-text {
  font-size: 0.95rem;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  box-orient: vertical;
}

:root.light .marketing-tip-text {
  color: var(--casino-text);
}

:root.light .marketing-tips-title {
  color: var(--casino-text);
}

.marketing-tips-navigation {
  display: flex;
  justify-content: center;
  gap: 0.4rem;
  margin-top: 0.5rem;
}

.tip-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
}

:root.light .tip-indicator {
  background-color: rgba(0, 0, 0, 0.2);
}

.tip-indicator.active {
  background-color: #22c55e;
  transform: scale(1.2);
}

.tip-indicator:hover {
  background-color: rgba(34, 197, 94, 0.5);
}

/* Chat y soporte */
.chat-support {
  background-color: rgba(30, 27, 46, 0.7);
  border-radius: 0.75rem;
  padding: 1.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-support-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chat-support-title svg {
  color: var(--casino-primary);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.chat-input {
  display: flex;
  gap: 0.5rem;
}

.chat-input input {
  flex: 1;
  background-color: rgba(255, 255, 255, 0.05);
  border: none;
  border-radius: 20px;
  padding: 0.75rem 1rem;
  color: var(--casino-text);
}

.chat-input button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, var(--casino-primary), var(--casino-secondary));
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chat-input button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

/* Notificaciones */
.notifications {
  margin-top: auto;
}

.notifications-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 0.75rem;
  transition: all 0.2s ease;
}

.notification-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.notification-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(110, 142, 251, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.notification-time {
  font-size: 0.75rem;
  color: var(--casino-text-muted);
}

/* Responsive */
@media (max-width: 1200px) {
  .casino-layout {
    grid-template-columns: var(--sidebar-width) 1fr;
    grid-template-areas:
      "sidebar header"
      "sidebar main";
  }

  .casino-rightbar {
    display: none;
  }

  .featured-promos {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .casino-layout {
    grid-template-columns: 1fr;
    grid-template-areas:
      "header"
      "main";
  }

  .casino-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 250px;
    height: 100vh;
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem 0;
    z-index: 200;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .casino-sidebar.expanded {
    transform: translateX(0);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
  }

  .sidebar-logo {
    display: flex;
    justify-content: center;
    width: 100%;
    margin-bottom: 2rem;
  }

  .sidebar-nav {
    flex-direction: column;
    width: 100%;
  }

  .sidebar-nav-item {
    justify-content: flex-start;
    padding: 0.75rem 1.5rem;
  }

  .sidebar-nav-item-text {
    opacity: 1;
  }

  .sidebar-nav-item-icon {
    margin-right: 1rem;
  }

  /* Overlay para cuando el menú móvil está abierto */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 90;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    height: 100vh;
    width: 100vw;
  }

  .mobile-menu-overlay.active {
    opacity: 1;
    pointer-events: auto;
  }

  .featured-promos {
    grid-template-columns: 1fr;
  }

  .main-banner {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }

  .banner-content {
    max-width: 100%;
    margin-bottom: 1.5rem;
  }

  .banner-image {
    position: relative;
    right: auto;
    height: 150px;
  }
}

/* Animaciones y efectos */
.glow-effect {
  box-shadow: 0 0 15px rgba(110, 142, 251, 0.5);
  animation: glow 2s infinite alternate;
}

/* Estilos para el bloque de tutora asignada */
.glass-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.7), rgba(16, 185, 129, 0.5));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  overflow: hidden;
}

:root.light .glass-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(16, 185, 129, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  overflow: hidden;
}

/* Estilos para el bloque de nivel actual */
.glass-effect {
  background: rgba(30, 27, 46, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  overflow: hidden;
}

:root.light .glass-effect {
  background: rgba(245, 245, 250, 0.9);
  border: 1px solid rgba(139, 92, 246, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  overflow: hidden;
}

:root.light .bg-card\/80 {
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(139, 92, 246, 0.1);
}

:root.light .bg-card\/50 {
  background-color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(139, 92, 246, 0.1);
}

/* Estilos para títulos en modo claro */
:root.light .text-3xl.font-bold,
:root.light .text-2xl.font-bold,
:root.light .text-xl.font-semibold,
:root.light .text-xl.font-bold,
:root.light h1.text-3xl.font-bold,
:root.light h2.text-xl.font-bold,
:root.light h2.text-xl.font-semibold {
  color: white;
}

/* Estilos específicos para títulos en páginas de niveles, logros y clasificación */
:root.light .glass-card h2.text-xl.font-bold,
:root.light .glass-card h2.text-xl.font-semibold {
  color: white;
}

/* Estilos para el nombre de la tutora en modo claro */
:root.light .glass-card h3.text-xl.font-bold {
  color: var(--casino-text);
}

/* Estilos para textos en el bloque de nivel actual en modo claro */
:root.light .glass-effect h2,
:root.light .glass-effect h3,
:root.light .glass-effect p,
:root.light .glass-effect .text-xl.font-bold,
:root.light .glass-effect .text-lg.font-semibold,
:root.light .glass-effect .text-xl.font-semibold,
:root.light .glass-effect .text-foreground\/60,
:root.light .glass-effect .text-foreground\/70,
:root.light .glass-effect .text-sm.font-medium,
:root.light .glass-effect .text-sm.text-foreground\/70,
:root.light .glass-effect .text-xs.text-foreground\/60,
:root.light .glass-effect .text-2xl.font-bold {
  color: var(--casino-text);
}

/* Estilos para textos en bloques específicos en modo claro */
:root.light .glass-card h3,
:root.light .glass-card p,
:root.light .glass-card .text-lg.font-semibold,
:root.light .glass-card .text-foreground\/60,
:root.light .glass-card .text-foreground\/70 {
  color: var(--casino-text);
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px rgba(110, 142, 251, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(110, 142, 251, 0.8);
  }
}

.pulse-effect {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.float-effect {
  animation: float 4s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Indicador de estado activo */
.status-indicator {
  position: absolute;
  bottom: -3px;
  right: -3px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #10b981;
  border: 2px solid var(--casino-background);
  animation: pulse 2s infinite;
  transform: translate(50%, 50%);
  z-index: 10;
}

/* Badges y etiquetas */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  position: relative;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:root.light .badge {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.badge-primary {
  background-color: rgba(110, 142, 251, 0.2);
  color: var(--casino-primary);
}

.badge-secondary {
  background-color: rgba(167, 119, 227, 0.2);
  color: var(--casino-secondary);
}

.badge-accent {
  background-color: rgba(236, 72, 153, 0.2);
  color: var(--casino-accent);
}

.badge-new {
  background-color: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.badge-hot {
  background-color: rgba(139, 92, 246, 0.2);
  color: #8b5cf6;
}

:root.light .badge-primary {
  background-color: rgba(110, 142, 251, 0.3);
}

:root.light .badge-secondary {
  background-color: rgba(167, 119, 227, 0.3);
}

:root.light .badge-accent {
  background-color: rgba(236, 72, 153, 0.3);
}

:root.light .badge-new {
  background-color: rgba(34, 197, 94, 0.3);
}

:root.light .badge-hot {
  background-color: rgba(139, 92, 246, 0.3);
}

/* Botones de filtro */
.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.filter-buttons::-webkit-scrollbar {
  display: none;
}

.filter-button {
  padding: 0.6rem 1.4rem;
  border-radius: 0.75rem;
  background-color: rgba(0, 0, 0, 0.8);
  font-size: 0.85rem;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: white;
  display: inline-flex;
  align-items: center;
  font-weight: 500;
  margin-right: 0.75rem;
  margin-bottom: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.filter-button:hover {
  background-color: rgba(0, 0, 0, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.filter-button.active {
  background-color: var(--casino-primary);
  color: white;
  box-shadow: 0 3px 10px rgba(110, 142, 251, 0.3);
  transform: translateY(-1px);
}

:root.light .filter-button {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
