'use client';

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Copy, Check, ExternalLink, Trash2, AlertCircle } from "lucide-react";
import { LanguageSelector } from "@/components/LanguageSelector";
import { useLanguage } from "@/contexts/LanguageContext";
import { useUserLevel } from "@/contexts/UserLevelContext";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabase";
import { toast } from "sonner";
import BackButton from "@/components/ui/back-button";

interface AffiliateLink {
  id: string;
  app_id: number;
  url: string;
  clicks: number;
  created_at: string;
  apps: {
    name: string;
    icon: string;
    price: number;
    url: string;
  };
}

export default function MyLinksPage() {
  const { t } = useLanguage();
  const { level, commission } = useUserLevel();
  const { user } = useAuth();
  const [links, setLinks] = useState<AffiliateLink[]>([]);
  const [loading, setLoading] = useState(true);
  const [copiedLinks, setCopiedLinks] = useState<{[key: string]: boolean}>({});
  const [stats, setStats] = useState<{
    totalLinks: number;
    totalClicks: number;
    potentialEarnings: number;
  }>({
    totalLinks: 0,
    totalClicks: 0,
    potentialEarnings: 0
  });

  useEffect(() => {
    if (user) {
      loadUserLinks(user.id);
    }
  }, [user]);

  // Cargar enlaces del usuario
  const loadUserLinks = async (userId: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('affiliate_links')
        .select(`
          id,
          app_id,
          url,
          clicks,
          created_at,
          apps (name, icon, price, url)
        `)
        .eq('user_id', userId);
        
      if (error) throw error;
      
      if (data) {
        setLinks(data as unknown as AffiliateLink[]);
        
        // Calcular estadísticas
        const totalLinks = data.length;
        const totalClicks = data.reduce((sum, link) => sum + (link.clicks || 0), 0);
        const potentialEarnings = data.reduce((sum, link) => {
          const appPrice = link.apps?.price || 0;
          return sum + ((link.clicks || 0) * appPrice * commission / 100);
        }, 0);
        
        setStats({
          totalLinks,
          totalClicks,
          potentialEarnings
        });
      }
    } catch (error) {
      console.error('Error al cargar enlaces:', error);
      toast.error('Error al cargar tus enlaces');
    } finally {
      setLoading(false);
    }
  };

  // Copiar enlace al portapapeles
  const handleCopyLink = (id: string, url: string) => {
    navigator.clipboard.writeText(url);
    setCopiedLinks({...copiedLinks, [id]: true});
    
    setTimeout(() => {
      setCopiedLinks({...copiedLinks, [id]: false});
    }, 2000);
    
    toast.success('Enlace copiado al portapapeles');
  };
  
  // Eliminar enlace
  const handleDeleteLink = async (id: string) => {
    if (!confirm('¿Estás seguro de que quieres eliminar este enlace?')) return;
    
    try {
      const { error } = await supabase
        .from('affiliate_links')
        .delete()
        .eq('id', id);
        
      if (error) throw error;
      
      // Actualizar la lista de enlaces
      setLinks(links.filter(link => link.id !== id));
      toast.success('Enlace eliminado correctamente');
    } catch (error) {
      console.error('Error al eliminar enlace:', error);
      toast.error('Error al eliminar el enlace');
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-7xl mt-20">
      <BackButton />
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">{t('Mis Enlaces')}</h1>
          <p className="text-foreground/70 mt-1">
            {t('Gestiona y comparte tus enlaces de afiliado para generar comisiones')}
          </p>
        </div>

        {/* Estadísticas */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="p-4 border border-border/50 bg-card/50">
            <div className="text-sm text-foreground/70">Total de enlaces</div>
            <div className="text-2xl font-bold mt-1">{stats.totalLinks}</div>
          </Card>
          <Card className="p-4 border border-border/50 bg-card/50">
            <div className="text-sm text-foreground/70">Clics totales</div>
            <div className="text-2xl font-bold mt-1">{stats.totalClicks}</div>
          </Card>
          <Card className="p-4 border border-border/50 bg-card/50">
            <div className="text-sm text-foreground/70">Ganancias potenciales</div>
            <div className="text-2xl font-bold mt-1">${stats.potentialEarnings.toFixed(2)} USD</div>
          </Card>
        </div>

        {/* Lista de enlaces */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Mis enlaces de afiliado</h2>
          
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin text-primary mb-2">⟳</div>
              <p>Cargando tus enlaces...</p>
            </div>
          ) : links.length === 0 ? (
            <Card className="p-6 text-center border border-border/50 bg-card/50">
              <AlertCircle className="mx-auto mb-2 text-foreground/70" size={24} />
              <h3 className="text-lg font-medium">No tienes enlaces generados</h3>
              <p className="text-foreground/70 mt-1">
                Ve a la sección de Apps y genera enlaces para empezar a ganar comisiones
              </p>
              <Button className="mt-4" onClick={() => window.location.href = '/dashboard/cursos'}>
                Ir a Apps
              </Button>
            </Card>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {links.map(link => (
                <Card key={link.id} className="p-4 border border-border/50 bg-card/50">
                  <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="font-medium">{link.apps?.name || `App #${link.app_id}`}</div>
                        <div className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                          {commission}% comisión
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 p-2 bg-background/50 rounded-md border border-border/40 overflow-hidden mb-2">
                        <div className="truncate flex-1 text-sm">{link.url}</div>
                        <button
                          onClick={() => handleCopyLink(link.id, link.url)}
                          className="p-1.5 rounded-md hover:bg-primary/10 transition-colors"
                          aria-label="Copiar enlace"
                        >
                          {copiedLinks[link.id] ? <Check size={18} className="text-green-500" /> : <Copy size={18} />}
                        </button>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-foreground/70">
                        <div>Clics: {link.clicks || 0}</div>
                        <div>Creado: {new Date(link.created_at).toLocaleDateString()}</div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() => window.open(link.url, '_blank')}
                      >
                        <ExternalLink size={14} />
                        Probar
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1"
                        onClick={() => handleCopyLink(link.id, link.url)}
                      >
                        {copiedLinks[link.id] ? <Check size={14} /> : <Copy size={14} />}
                        {copiedLinks[link.id] ? 'Copiado' : 'Copiar'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1 text-destructive hover:bg-destructive/10"
                        onClick={() => handleDeleteLink(link.id)}
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
