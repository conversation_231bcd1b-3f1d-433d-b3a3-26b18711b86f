@tailwind base;
@tailwind components;
@tailwind utilities;

/* Importar fuentes */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Solitreo&display=swap');

/* Configurar fuente por defecto */
@layer base {
  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-[#9333ea] via-[#ec4899] to-[#facc15];
    background-size: 300% 100%;
    animation: gradient-shift 20s linear infinite;
  }

  .rotating-text {
    @apply font-solitreo;
    min-width: 180px;
    display: inline-block;
    text-align: center;
  }

  .hero-grid-bg {
    background-image: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 30px 30px;
    opacity: 0.2;
  }

  .animate-float {
    animation: float 4s ease-in-out infinite;
  }
}

@keyframes construct {
  0% { background-position: 0 0; }
  100% { background-position: 40px 40px; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes progress-pulse {
  0% { transform: translateX(-100%); opacity: 0.7; }
  50% { opacity: 0.3; }
  100% { transform: translateX(100%); opacity: 0.7; }
}

@keyframes sparkle {
  0% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); }
}

@keyframes fade-in {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes text-fade-in {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}

@keyframes text-fade-out {
  0% { opacity: 1; transform: translateY(0); }
  100% { opacity: 0; transform: translateY(-20px); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  20% { background-position: 25% 50%; }
  40% { background-position: 50% 50%; }
  60% { background-position: 75% 50%; }
  80% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(-5px) translateX(2px); }
  50% { transform: translateY(-10px) translateX(0px); }
  75% { transform: translateY(-5px) translateX(-2px); }
  100% { transform: translateY(0px) translateX(0px); }
}

@keyframes pulse-slow {
  0% { opacity: 0.5; }
  50% { opacity: 0.8; }
  100% { opacity: 0.5; }
}

@keyframes slideInRight {
  0% { transform: translateX(100%); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes glow {
  0% { opacity: 0.3; }
  50% { opacity: 0.6; }
  100% { opacity: 0.3; }
}

@keyframes appear {
  0% { opacity: 0.7; transform: scale(0.95); }
  50% { opacity: 1; transform: scale(1.02); }
  100% { opacity: 0.7; transform: scale(0.95); }
}

@layer base {
  /* Tema único - sin modo claro/oscuro */
  :root {
    --background: 220 33% 6%;
    --foreground: 230 10% 95%;
    --card: 220 40% 8%;
    --card-foreground: 230 10% 95%;
    --popover: 220 40% 8%;
    --popover-foreground: 230 10% 95%;
    --primary: 280 85% 65%;
    --primary-foreground: 230 10% 95%;
    --secondary: 220 40% 12%;
    --secondary-foreground: 230 10% 95%;
    --muted: 220 40% 14%;
    --muted-foreground: 220 10% 70%;
    --accent: 45 80% 50%;
    --accent-foreground: 230 10% 95%;
    --destructive: 350 90% 60%;
    --destructive-foreground: 220 10% 95%;
    --border: 220 40% 10%;
    --input: 220 40% 12%;
    --ring: 280 85% 65%;
    --radius: 0.6rem;
  }
}

@layer base {
  * {
    @apply border-transparent;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .container-custom {
    @apply w-full max-w-[1200px] mx-auto px-4;
  }

  /* Clase para aplicar aceleración por hardware */
  .hardware-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform;
  }

  /* Optimizaciones para móviles */
  .mobile-smooth-scroll {
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;
    overscroll-behavior-y: contain;
    scroll-behavior: smooth;
  }

  /* Igualar ancho de bloques de contadores en móviles */
  @media (max-width: 640px) {
    .hero-stats-container {
      display: flex;
      flex-direction: row;
      justify-content: center;
      width: 100%;
      max-width: 100%;
      margin: 0 auto;
      gap: 8px;
      padding: 0 8px;
    }

    .hero-stats-container > div {
      width: 48%;
      min-width: 0;
      max-width: 48%;
      flex: 0 0 48%;
      padding-left: 4px;
      padding-right: 4px;
    }

    /* Asegurar que el texto no se desborde */
    .hero-stats-container p {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
    }
  }

  /* Feedback táctil para móviles */
  .mobile-touch-friendly {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
    min-height: 44px; /* Altura mínima recomendada para elementos táctiles */
  }

  /* Efecto de feedback táctil */
  .mobile-touch-feedback {
    position: relative;
    overflow: hidden;
  }

  .mobile-touch-feedback:active::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: inherit;
    opacity: 0;
    animation: touch-ripple 0.4s ease-out;
  }

  @keyframes touch-ripple {
    0% { opacity: 0.5; transform: scale(0.8); }
    100% { opacity: 0; transform: scale(1.5); }
  }

  /* Optimizaciones para dispositivos de bajo rendimiento */
  @media (max-width: 768px), (prefers-reduced-motion: reduce) {
    .reduce-motion {
      transition: none !important;
      animation: none !important;
    }

    .simplify-effects {
      backdrop-filter: none !important;
      -webkit-backdrop-filter: none !important;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    }
  }

  /* Fondo único */
  .gradient-background {
    background: radial-gradient(circle at 50% 50%, rgba(140, 50, 250, 0.15) 0%, rgba(140, 50, 250, 0) 70%),
                linear-gradient(180deg, var(--background) 0%, hsl(220, 40%, 3%) 100%);
  }

  .card-school {
    @apply bg-card hover:bg-secondary transition-colors duration-200 rounded-xl backdrop-blur-sm border border-border/30 p-4 flex items-center gap-3;
  }

  .text-gradient {
    @apply bg-clip-text text-transparent;
    background-image: linear-gradient(to right, #9333ea, #ec4899, #f97316, #facc15);
  }

  /* Estilos para títulos */
  h1, h2, h3, h4, h5, h6 {
    @apply font-outfit font-bold;
  }

  /* Títulos */
  h1:not(.text-gradient), h2:not(.text-gradient), h3:not(.text-gradient) {
    @apply text-white;
  }

  .glow-effect {
    filter: drop-shadow(0 0 15px rgba(140, 50, 250, 0.6));
    animation: glow 2s infinite alternate;
  }

  /* Efecto de brillo especial para botones */
  .pink-glow-button {
    position: relative;
    z-index: 1;
    box-shadow: 0 0 20px 5px rgba(236, 72, 153, 0.7);
    animation: pinkPulse 2s infinite alternate;
  }

  .pink-glow-button::before {
    content: "";
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(45deg, #9333ea, #ec4899);
    border-radius: 9999px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
    animation: pinkPulse 2s infinite alternate;
  }

  @keyframes pinkPulse {
    0% {
      opacity: 0.5;
      box-shadow: 0 0 15px 5px rgba(236, 72, 153, 0.5);
    }
    100% {
      opacity: 0.8;
      box-shadow: 0 0 25px 8px rgba(236, 72, 153, 0.8);
    }
  }

  @keyframes glow {
    0% {
      filter: drop-shadow(0 0 10px rgba(140, 50, 250, 0.5));
    }
    100% {
      filter: drop-shadow(0 0 20px rgba(236, 72, 153, 0.7));
    }
  }

  .glass-card {
    @apply bg-card shadow-xl;
  }

  .dropdown-menu {
    @apply bg-card shadow-lg;
  }

  .neon-border {
    box-shadow: 0 0 5px rgba(140, 50, 250, 0.7),
                inset 0 0 5px rgba(140, 50, 250, 0.4);
  }

  .dropdown-menu {
    @apply fixed z-[9999] bg-card shadow-lg rounded-lg;
  }

  /* Estilos para el iframe de Hotmart */
  #inline_checkout iframe {
    background: linear-gradient(to bottom, #0f0f1a, #1a1a2e) !important;
    color-scheme: dark !important;
    border-radius: 0.75rem !important;
  }

  #inline_checkout {
    background: linear-gradient(to bottom, #0f0f1a, #1a1a2e) !important;
  }

  /* Estilos para botones */
  .btn-primary {
    @apply bg-primary text-white shadow-md;
    box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3);
  }

  .btn-primary:hover {
    @apply bg-primary/90;
    box-shadow: 0 6px 16px rgba(147, 51, 234, 0.4);
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground border-border/60;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .btn-secondary:hover {
    @apply bg-secondary/90 text-secondary-foreground;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }

  /* Estilos para las notificaciones FOMO */
  .glow-blue-subtle {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.15);
    animation: subtle-glow-blue 3s infinite alternate;
  }

  .glow-green-subtle {
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.15);
    animation: subtle-glow-green 3s infinite alternate;
  }

  @keyframes subtle-glow-blue {
    0% {
      box-shadow: 0 0 10px rgba(59, 130, 246, 0.1);
    }
    100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
    }
  }

  @keyframes subtle-glow-green {
    0% {
      box-shadow: 0 0 10px rgba(16, 185, 129, 0.1);
    }
    100% {
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
    }
  }

  /* Centrar notificaciones FOMO en dispositivos móviles */
  @media (max-width: 640px) {
    .fomo-notification {
      left: 50% !important;
      right: auto !important;
      transform: translateX(-50%) !important;
    }
  }
}
