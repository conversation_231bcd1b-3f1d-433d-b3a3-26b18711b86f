/* Estilos para el nuevo dashboard */
:root {
    /* Colores principales - <PERSON><PERSON> (por defecto) */
    --primary-color: #6e8efb;
    --secondary-color: #a777e3;
    --accent-color: #a855f7;
    --background-dark: #13111C;
    --background-light: #1E1B2E;
    --text-color: #ffffff;
    --text-muted: rgba(255, 255, 255, 0.7);
    --border-color: rgba(168, 85, 247, 0.2);

    /* Colores de tarjetas */
    --card-bg: rgba(30, 27, 46, 0.7);
    --card-border: rgba(168, 85, 247, 0.2);
    --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);

    /* Colores de estado */
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --error-color: #f87171;

    /* Tamaños */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 80px;
    --header-height: 80px;
    --border-radius: 12px;
    --card-radius: 16px;

    /* Transiciones */
    --transition-speed: 0.3s;
}

/* Modo Claro - Estilo Vidrio Esmerilado */
body.light-mode {
    /* Colores principales - Modo Claro */
    --background-dark: #f0f4ff;
    --background-light: #ffffff;
    --text-color: #333333;
    --text-muted: rgba(0, 0, 0, 0.6);
    --border-color: rgba(168, 85, 247, 0.3);

    /* Colores de tarjetas */
    --card-bg: rgba(255, 255, 255, 0.7);
    --card-border: rgba(168, 85, 247, 0.3);
    --card-shadow: 0 8px 32px rgba(110, 142, 251, 0.15);

    /* Fondo con gradiente suave */
    background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
}

/* Estilos específicos para el modo claro */
body.light-mode .sidebar {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(168, 85, 247, 0.2);
}

body.light-mode .main-header {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(168, 85, 247, 0.2);
}

body.light-mode .prompt-card,
body.light-mode .result-card,
body.light-mode .profile-card,
body.light-mode .gallery-item {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(168, 85, 247, 0.3);
    box-shadow: 0 8px 32px rgba(110, 142, 251, 0.1);
}

body.light-mode .nav-item:hover {
    background: rgba(110, 142, 251, 0.1);
}

body.light-mode .nav-item.active {
    background: rgba(110, 142, 251, 0.15);
    border-left: 3px solid var(--primary-color);
}

body.light-mode .prompt-textarea {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(168, 85, 247, 0.3);
}

body.light-mode .suggestion-chip {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(168, 85, 247, 0.2);
}

body.light-mode .suggestion-chip:hover {
    background: rgba(110, 142, 251, 0.15);
    border: 1px solid rgba(168, 85, 247, 0.4);
}

body.light-mode .action-btn {
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(168, 85, 247, 0.2);
}

body.light-mode .action-btn:hover {
    background: rgba(110, 142, 251, 0.15);
    border: 1px solid rgba(168, 85, 247, 0.4);
}

body.light-mode .result-container {
    background: rgba(240, 244, 255, 0.5);
}

body.light-mode .empty-result {
    color: rgba(0, 0, 0, 0.4);
}

body.light-mode .empty-icon {
    color: rgba(0, 0, 0, 0.3);
}

/* Estilos generales */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-light) 100%);
    color: var(--text-color);
    line-height: 1.6;
    overflow-x: hidden;
    min-height: 100vh;
}

a {
    text-decoration: none;
    color: inherit;
}

button {
    cursor: pointer;
    border: none;
    background: none;
    font-family: inherit;
    color: inherit;
}

/* Contenedor principal */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: rgba(30, 27, 46, 0.8);
    backdrop-filter: blur(10px);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 100;
    transition: transform var(--transition-speed) ease, width var(--transition-speed) ease;
}

.sidebar-header {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-logo {
    width: 32px;
    height: 32px;
}

.sidebar-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.accent-text {
    color: var(--primary-color);
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.ai-text {
    color: var(--accent-color);
    background: linear-gradient(90deg, #a855f7, #ff6b6b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.sidebar-nav {
    flex: 1;
    padding: 1.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.8rem 1.5rem;
    color: var(--text-muted);
    transition: all var(--transition-speed) ease;
    border-left: 3px solid transparent;
    gap: 12px;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
}

.nav-item.active {
    background: rgba(110, 142, 251, 0.1);
    color: var(--text-color);
    border-left: 3px solid var(--primary-color);
}



.nav-icon {
    font-size: 1.2rem;
    width: 24px;
    text-align: center;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--text-muted);
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 500;
}

.user-plan {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.user-plan.premium {
    color: var(--accent-color);
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    padding: 0.8rem;
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-muted);
    transition: all var(--transition-speed) ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

/* Contenido principal */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-header {
    height: var(--header-height);
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(30, 27, 46, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 90;
}

.mobile-menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

.header-title h2 {
    font-size: 1.5rem;
    font-weight: 500;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.theme-toggle, .notifications {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.theme-toggle:hover, .notifications:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Secciones de contenido */
.content-sections {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Tarjetas */
.card-header {
    padding: 1.2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header h3 {
    font-size: 1.2rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-header h3 i {
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Sección del Generador */
.generator-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.prompt-card, .result-card {
    background: var(--card-bg);
    border-radius: var(--card-radius);
    border: 1px solid var(--card-border);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.8rem;
    font-weight: 500;
}

.prompt-textarea {
    width: 100%;
    min-height: 120px;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-color);
    font-family: inherit;
    font-size: 1rem;
    resize: vertical;
    transition: all var(--transition-speed) ease;
}

.prompt-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(110, 142, 251, 0.2);
}

.prompt-suggestions {
    margin-bottom: 1.5rem;
}

.prompt-suggestions h4 {
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 0.8rem;
    color: var(--text-muted);
}

.suggestion-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-chip {
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.suggestion-chip:hover {
    background: rgba(110, 142, 251, 0.2);
}

.form-actions {
    display: flex;
    justify-content: flex-end;
}

.generate-btn {
    padding: 0.8rem 1.5rem;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
    color: white;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed) ease;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.generate-btn:active {
    transform: translateY(0);
}

.result-container {
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.empty-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    padding: 2rem;
    text-align: center;
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.result-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(30, 27, 46, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.spinner-container {
    margin-bottom: 1rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.result-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.action-btn {
    padding: 0.8rem 1.2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed) ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Sección de la Galería */
.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.gallery-filters {
    display: flex;
    gap: 1rem;
}

.search-container {
    position: relative;
}

.search-input {
    padding: 0.8rem 1rem 0.8rem 2.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-color);
    width: 250px;
    font-family: inherit;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-icon {
    position: absolute;
    left: 0.8rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.filter-dropdown {
    position: relative;
}

.filter-btn {
    padding: 0.8rem 1.2rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    min-width: 150px;
    z-index: 10;
    display: none;
}

.filter-dropdown:hover .filter-menu {
    display: block;
}

.filter-option {
    display: block;
    padding: 0.8rem 1.2rem;
    transition: all var(--transition-speed) ease;
}

.filter-option:hover {
    background: rgba(255, 255, 255, 0.05);
}

.filter-option.active {
    background: rgba(110, 142, 251, 0.1);
    color: var(--primary-color);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    background: var(--card-bg);
    border-radius: var(--card-radius);
    border: 1px solid var(--card-border);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow);
}

.gallery-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-info {
    padding: 1rem;
}

.gallery-prompt {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 0.8rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.gallery-date {
    font-size: 0.8rem;
    color: var(--text-muted);
}

.gallery-actions {
    display: flex;
    justify-content: space-between;
    padding: 0.8rem 1rem;
    border-top: 1px solid var(--border-color);
}

.gallery-action {
    color: var(--text-muted);
    font-size: 1rem;
    transition: all var(--transition-speed) ease;
}

.gallery-action:hover {
    color: var(--text-color);
}

.empty-gallery {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    background: var(--card-bg);
    border-radius: var(--card-radius);
    border: 1px solid var(--card-border);
}

.empty-gallery .empty-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
}

.create-first-btn {
    margin-top: 1.5rem;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
    color: white;
    font-weight: 500;
    transition: all var(--transition-speed) ease;
}

.create-first-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

/* Sección del Perfil */
.profile-container {
    max-width: 900px;
    margin: 0 auto;
}

.profile-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: var(--text-muted);
}

.profile-title h3 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.plan-badge {
    display: inline-block;
    padding: 0.4rem 1rem;
    background: rgba(168, 85, 247, 0.1);
    color: var(--accent-color);
    border-radius: 20px;
    font-size: 0.9rem;
}

.profile-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

@media (max-width: 768px) {
    .profile-cards {
        grid-template-columns: 1fr;
    }
}

.profile-card {
    background: var(--card-bg);
    border-radius: var(--card-radius);
    border: 1px solid var(--card-border);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.profile-field {
    margin-bottom: 1.2rem;
}

.profile-field:last-child {
    margin-bottom: 0;
}

.profile-field label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-bottom: 0.3rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
}

.stat-value {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.3s ease;
}

/* Animación para actualizar estadísticas */
.stat-updating {
    animation: stat-pulse 1s ease;
}

@keyframes stat-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
        background: linear-gradient(90deg, var(--accent-color), var(--primary-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    100% {
        transform: scale(1);
    }
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.subscription-card {
    border-color: var(--accent-color);
}

.subscription-status {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.status-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.status-icon.active {
    background: rgba(74, 222, 128, 0.1);
    color: var(--success-color);
}

.status-text h4 {
    margin-bottom: 0.3rem;
}

.subscription-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 8px;
}

.feature-item i {
    color: var(--accent-color);
}

/* Popup de Premium */
.premium-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(19, 17, 28, 0.9);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-speed) ease;
}

.premium-popup.active {
    opacity: 1;
    visibility: visible;
}

.premium-content {
    background: var(--card-bg);
    border-radius: var(--card-radius);
    border: 1px solid var(--card-border);
    box-shadow: var(--card-shadow);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    animation: popIn 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes popIn {
    from { transform: scale(0.9); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.popup-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.popup-header h2 {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.popup-header h2 i {
    color: var(--accent-color);
}

.close-popup-x {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    font-size: 1.2rem;
    transition: all var(--transition-speed) ease;
}

.close-popup-x:hover {
    background: rgba(255, 255, 255, 0.1);
}

.premium-subtitle {
    padding: 1.5rem 1.5rem 0;
    text-align: center;
    font-size: 1.1rem;
}

.premium-features {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.premium-feature {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.premium-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(168, 85, 247, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--accent-color);
}

.premium-feature h4 {
    margin-bottom: 0.5rem;
}

.premium-feature p {
    font-size: 0.9rem;
    color: var(--text-muted);
}

.premium-price {
    text-align: center;
    padding: 1.5rem;
}

.price-value {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.price-period {
    font-size: 1rem;
    color: var(--text-muted);
}

.popup-actions {
    padding: 0 1.5rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.premium-btn {
    padding: 1rem;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 8px;
    color: white;
    font-weight: 500;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all var(--transition-speed) ease;
}

.premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(110, 142, 251, 0.3);
}

.close-popup {
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    text-align: center;
    transition: all var(--transition-speed) ease;
}

.close-popup:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Overlay para móvil */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(19, 17, 28, 0.8);
    z-index: 90;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-speed) ease;
}

/* Responsive */
@media (max-width: 1024px) {
    .generator-container {
        grid-template-columns: 1fr;
    }

    .profile-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .mobile-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    .gallery-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .gallery-filters {
        width: 100%;
    }

    .search-input {
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .premium-features {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .main-header {
        padding: 0 1rem;
    }

    .content-sections {
        padding: 1rem;
    }

    .header-title h2 {
        font-size: 1.2rem;
    }

    .profile-header {
        flex-direction: column;
        text-align: center;
    }

    .gallery-filters {
        flex-direction: column;
    }

    .filter-dropdown {
        width: 100%;
    }

    .filter-btn {
        width: 100%;
        justify-content: center;
    }

    .result-actions {
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }
}
