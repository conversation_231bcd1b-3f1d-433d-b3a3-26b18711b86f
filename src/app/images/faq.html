<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preguntas Frecuentes - Flasti Images</title>
    <link rel="icon" type="image/svg+xml" href="images/logo.svg">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="responsive.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #fff;
            background: linear-gradient(135deg, #13111C 0%, #1E1B2E 100%);
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #fff;
        }

        .logo img {
            height: 40px;
            margin-right: 10px;
        }

        .logo h1 {
            font-size: 24px;
            margin: 0;
        }

        .ai-text {
            color: #6e8efb;
        }

        .content {
            background-color: rgba(30, 27, 46, 0.5);
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-top: 80px;
        }

        h1 {
            color: #fff;
            margin-top: 0;
            margin-bottom: 30px;
            font-size: 32px;
            text-align: center;
        }

        .faq-item {
            margin-bottom: 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 20px;
        }

        .faq-item:last-child {
            border-bottom: none;
        }

        .question {
            font-size: 20px;
            font-weight: bold;
            color: #a855f7;
            margin-bottom: 10px;
        }

        .answer {
            font-size: 16px;
            line-height: 1.6;
        }

        .back-button {
            display: inline-block;
            margin-top: 30px;
            padding: 12px 25px;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .content {
                padding: 20px;
                margin-top: 60px;
            }

            h1 {
                font-size: 28px;
            }

            .question {
                font-size: 18px;
            }
        }

        /* Estilos para la navegación */
        nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(19, 17, 28, 0.9);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 15px 0;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
        }

        .nav-logo-img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
        }

        .nav-logo h1 {
            font-size: 20px;
            margin: 0;
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html" class="logo">
                    <img src="images/logo.svg" alt="Flasti Images Logo" class="nav-logo-img">
                    <h1>Flasti <span class="ai-text">Images</span></h1>
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="content">
            <h1>Preguntas Frecuentes</h1>

            <div class="faq-item">
                <div class="question">¿Qué es Flasti Images?</div>
                <div class="answer">
                    Flasti Images es una plataforma de generación de imágenes impulsada por inteligencia artificial. Permite a los usuarios crear imágenes únicas y personalizadas a partir de descripciones textuales, utilizando tecnología de IA avanzada.
                </div>
            </div>

            <div class="faq-item">
                <div class="question">¿Cómo funciona la generación de imágenes?</div>
                <div class="answer">
                    Nuestra tecnología utiliza modelos de IA entrenados con millones de imágenes. Cuando introduces una descripción textual, nuestro sistema interpreta el texto y genera una imagen única que corresponde a tu descripción. El proceso toma solo unos segundos y el resultado es una imagen completamente original.
                </div>
            </div>

            <div class="faq-item">
                <div class="question">¿Cuántas imágenes puedo generar con la cuenta gratuita?</div>
                <div class="answer">
                    Con la cuenta gratuita, puedes generar hasta 5 imágenes. Para generar más imágenes, necesitarás actualizar a nuestro plan Premium, que ofrece generación ilimitada de imágenes por un pago único de $5.
                </div>
            </div>

            <div class="faq-item">
                <div class="question">¿Qué incluye el plan Premium?</div>
                <div class="answer">
                    El plan Premium incluye generación ilimitada de imágenes, máxima calidad de generación, almacenamiento en la nube de tus creaciones, soporte prioritario y acceso de por vida por un pago único de $5.
                </div>
            </div>

            <div class="faq-item">
                <div class="question">¿Puedo usar las imágenes generadas para fines comerciales?</div>
                <div class="answer">
                    Sí, todas las imágenes generadas con Flasti Images son de tu propiedad y puedes utilizarlas para cualquier propósito, incluyendo proyectos comerciales, sin restricciones ni regalías.
                </div>
            </div>

            <div class="faq-item">
                <div class="question">¿Cómo puedo obtener los mejores resultados?</div>
                <div class="answer">
                    Para obtener los mejores resultados, te recomendamos proporcionar descripciones detalladas y específicas. Incluye detalles sobre el estilo, colores, ambiente y elementos que deseas en la imagen. Cuanto más específico seas, mejores serán los resultados.
                </div>
            </div>

            <div class="faq-item">
                <div class="question">¿Qué métodos de pago aceptan?</div>
                <div class="answer">
                    Aceptamos pagos a través de Hotmart, que permite múltiples métodos de pago incluyendo tarjetas de crédito, PayPal y transferencias bancarias, dependiendo de tu ubicación.
                </div>
            </div>

            <div class="faq-item">
                <div class="question">¿Ofrecen reembolsos?</div>
                <div class="answer">
                    Sí, ofrecemos una garantía de satisfacción de 7 días. Si no estás satisfecho con el servicio, puedes solicitar un reembolso completo dentro de los primeros 7 días después de la compra.
                </div>
            </div>

            <a href="index.html" class="back-button">Volver al Inicio</a>
        </div>

        <footer>
            <p>© 2025 Flasti Images | Todos los derechos reservados.</p>
        </footer>
    </div>
</body>
</html>
