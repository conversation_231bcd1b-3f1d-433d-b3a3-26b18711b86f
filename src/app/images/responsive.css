/* Estilos responsivos para todas las páginas de Flasti Images */

/* Estilos generales responsivos */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }

    .hero-section {
        padding: 6rem 1rem 3rem;
    }

    .steps-container, .pricing-container, .examples-gallery {
        flex-direction: column;
        align-items: center;
    }

    .step-item, .pricing-card, .example-item {
        width: 100%;
        max-width: 500px;
        margin-bottom: 2rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-column {
        margin-bottom: 2rem;
    }
}

/* Tablets y dispositivos medianos */
@media (max-width: 768px) {
    /* Navegación */
    .nav-container {
        padding: 0.8rem 1rem;
        justify-content: space-between;
    }

    .nav-logo h1 {
        font-size: 1.2rem;
    }

    .nav-logo-img {
        width: 25px;
        height: 25px;
    }

    .login-premium-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    /* <PERSON>ú móvil */
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        z-index: 100;
        order: 3;
    }

    .nav-menu {
        position: fixed;
        top: 0;
        left: -100%;
        width: 80%;
        height: 100vh;
        background: rgba(19, 17, 28, 0.95);
        backdrop-filter: blur(10px);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: left 0.3s ease;
        z-index: 99;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-link {
        margin: 1rem 0;
        font-size: 1.2rem;
    }

    .nav-buttons {
        order: 2;
    }

    .nav-menu .nav-link {
        margin: 1rem 0;
    }

    /* Sección Hero */
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .hero-description {
        font-size: 1rem;
        max-width: 100%;
    }

    .get-premium-btn {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }

    /* Secciones */
    .section-title {
        font-size: 1.8rem;
    }

    .info-section, .examples-section, .pricing-section {
        padding: 3rem 1rem;
    }

    /* Checkout */
    .checkout-content {
        flex-direction: column;
    }

    .checkout-info, .checkout-form {
        width: 100%;
        margin-bottom: 2rem;
    }

    /* Formularios de autenticación */
    .auth-card {
        width: 90%;
        padding: 25px;
    }

    .auth-title {
        font-size: 24px;
    }

    .form-group input {
        padding: 12px;
    }

    .auth-button {
        padding: 12px 0;
    }

    /* Páginas de información */
    .content {
        padding: 20px;
    }
}

/* Dispositivos móviles pequeños */
@media (max-width: 480px) {
    /* Navegación para móviles pequeños */
    .nav-container {
        padding: 0.6rem 0.8rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 60px;
    }

    .nav-logo {
        flex: 0 0 auto;
        margin-right: auto;
        display: flex;
        align-items: center;
        position: relative;
        top: 4px; /* Bajar el logo un poco más */
    }

    .nav-logo h1 {
        font-size: 1rem;
    }

    .nav-logo-img {
        width: 20px;
        height: 20px;
        margin-right: 5px;
    }

    .nav-menu {
        width: 100%;
    }

    .nav-buttons {
        display: flex;
        align-items: center;
        position: relative;
        top: -3px; /* Subir un poco más los botones */
    }

    .login-button {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
        margin-right: 0.8rem;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-menu-toggle {
        font-size: 1.2rem;
        width: 32px;
        height: 32px;
        display: flex !important; /* Forzar visualización en móviles */
        align-items: center;
        justify-content: center;
        padding: 0;
    }

    /* Sección Hero */
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-description {
        font-size: 0.9rem;
    }

    .get-premium-btn {
        width: 100%;
        padding: 0.8rem;
    }

    /* Generador */
    .generator-container {
        flex-direction: column;
    }

    .prompt-card, .result-card {
        width: 100%;
        margin: 0 0 1rem 0;
    }

    .prompt-textarea {
        min-height: 80px;
    }

    .suggestion-chips {
        flex-wrap: wrap;
        justify-content: center;
    }

    .suggestion-chip {
        margin: 0.25rem;
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    /* Secciones */
    .section-title {
        font-size: 1.5rem;
    }

    /* Checkout */
    .feature-item {
        flex-direction: column;
        text-align: center;
    }

    .feature-icon {
        margin-bottom: 0.5rem;
    }

    /* Formularios de autenticación */
    .auth-card {
        padding: 20px;
    }

    .auth-title {
        font-size: 20px;
    }

    .form-group input {
        padding: 10px;
        font-size: 14px;
    }

    .auth-button {
        padding: 10px 0;
        font-size: 16px;
    }

    /* Footer profesional para móviles */
    .main-footer {
        padding: 2.5rem 0 1rem;
        background: linear-gradient(180deg, rgba(19, 17, 28, 0.8) 0%, rgba(19, 17, 28, 1) 100%);
        border-top: 1px solid rgba(168, 85, 247, 0.2);
    }

    .footer-container {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1.5rem;
    }

    .footer-logo {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 1rem;
    }

    .footer-logo-img {
        width: 40px;
        height: 40px;
        margin-bottom: 0.5rem;
    }

    .footer-logo h3 {
        font-size: 1.2rem;
        margin: 0;
    }

    .footer-links {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        width: 100%;
    }

    .footer-column {
        text-align: center;
    }

    .footer-column h4 {
        font-size: 0.85rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
    }

    .footer-column ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .footer-column ul li {
        margin-bottom: 0.8rem;
    }

    .footer-column ul li a {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.7);
        text-decoration: none;
        transition: color 0.2s ease;
    }

    .footer-column ul li a:hover {
        color: var(--accent-color);
    }

    .footer-bottom {
        margin-top: 2rem;
        padding-top: 1rem;
        text-align: center;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .copyright {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.5);
    }

    /* Dashboard */
    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        padding: 1rem;
    }

    .user-menu {
        margin-top: 1rem;
        width: 100%;
        justify-content: space-between;
    }

    .dashboard-content {
        flex-direction: column;
    }

    .dashboard-nav {
        width: 100%;
        overflow-x: auto;
        padding: 0.5rem;
        flex-direction: row;
        justify-content: flex-start;
        border-right: none;
        border-bottom: 1px solid rgba(168, 85, 247, 0.2);
    }

    .nav-item {
        padding: 0.5rem 1rem;
        flex-direction: column;
        text-align: center;
        min-width: 80px;
    }

    .dashboard-main {
        padding: 1rem;
    }

    .generator-container {
        flex-direction: column;
    }

    .prompt-container, .result-container {
        width: 100%;
    }

    .result-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .action-btn {
        margin: 0.5rem;
    }

    .profile-container {
        padding: 1rem;
    }

    .profile-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .profile-field {
        margin-bottom: 1rem;
        width: 100%;
    }

    /* Premium popup */
    .premium-content {
        width: 95%;
        padding: 1rem;
    }

    .premium-features {
        flex-direction: column;
    }

    .premium-feature {
        margin-bottom: 1.5rem;
    }

    .premium-btn, .close-popup {
        padding: 0.8rem;
    }
}

/* Ajustes específicos para el nuevo dashboard */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -100%;
        width: 80%;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .mobile-menu-toggle {
        display: block;
        cursor: pointer;
        font-size: 1.5rem;
    }

    .mobile-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
    }

    .mobile-overlay.active {
        display: block;
    }

    .generator-container {
        grid-template-columns: 1fr;
    }

    .gallery-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .gallery-filters {
        width: 100%;
        flex-direction: column;
    }

    .search-container {
        width: 100%;
    }

    .search-input {
        width: 100%;
    }

    .filter-dropdown {
        width: 100%;
    }

    .filter-btn {
        width: 100%;
        justify-content: center;
    }

    .profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .profile-cards {
        grid-template-columns: 1fr;
    }
}

/* Ajustes para páginas de información (FAQ, Ayuda, etc.) */
@media (max-width: 768px) {
    .content {
        margin-top: 60px;
        padding: 20px;
    }

    h1 {
        font-size: 24px;
    }

    h2 {
        font-size: 20px;
    }

    .faq-item .question {
        font-size: 18px;
    }

    .back-button {
        width: 100%;
        text-align: center;
    }

    table, th, td {
        font-size: 14px;
    }

    th, td {
        padding: 8px;
    }
}

/* Ajustes para la página de checkout */
@media (max-width: 768px) {
    .checkout-container {
        padding: 1rem;
    }

    .checkout-header {
        margin-bottom: 1.5rem;
    }

    .checkout-logo {
        width: 30px;
        height: 30px;
    }

    .checkout-header h1 {
        font-size: 1.5rem;
    }

    .checkout-content {
        flex-direction: column;
    }

    .checkout-info, .checkout-form {
        width: 100%;
    }

    .checkout-info {
        margin-bottom: 1.5rem;
    }

    .feature-item {
        padding: 1rem;
    }

    .feature-icon {
        font-size: 1.5rem;
    }

    .price-value {
        font-size: 2.5rem;
    }

    .hotmart-form-wrapper {
        max-height: 400px;
    }
}

/* Ajustes para formularios de autenticación */
@media (max-width: 480px) {
    .auth-container {
        padding: 10px;
    }

    .auth-card {
        padding: 15px;
    }

    .auth-logo {
        width: 30px;
        height: 30px;
    }

    .auth-header h1 {
        font-size: 1.2rem;
    }

    .auth-title {
        font-size: 18px;
        margin-bottom: 15px;
    }

    .form-group {
        margin-bottom: 12px;
    }

    .form-group input {
        padding: 8px;
        font-size: 14px;
    }

    .auth-button {
        padding: 10px 0;
        font-size: 14px;
    }

    .checkbox-group label {
        font-size: 12px;
    }
}
