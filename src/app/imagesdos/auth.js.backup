// Configuración de Supabase
const SUPABASE_URL = 'https://lflxpqryawqrbpdxvmka.supabase.co';
const SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImxmbHhwcXJ5YXdxcmJwZHh2bWthIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MTI3NDEsImV4cCI6MjA1ODk4ODc0MX0.QMmPkbK9T7ZHZygm5eEdrNA5nTLBWx-3CRw5TdpGaB0';

// Verificar que Supabase esté disponible
let supabaseClient;
try {
    if (typeof supabase !== 'undefined') {
        supabaseClient = supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        console.log('Cliente Supabase inicializado correctamente');
    } else {
        console.error('Error: Supabase no está definido');
        alert('Error al inicializar la autenticación. Por favor, recarga la página.');
    }
} catch (error) {
    console.error('Error al inicializar Supabase:', error);
    alert('Error al inicializar la autenticación. Por favor, recarga la página.');
}

document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM cargado, inicializando autenticación...');

    // Verificar si el usuario ya está autenticado
    checkAuth();

    // Configurar formularios
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');

    if (loginForm) {
        console.log('Formulario de login encontrado, configurando evento...');

        // Configurar el botón de submit directamente
        const loginButton = loginForm.querySelector('button[type="submit"]');
        if (loginButton) {
            loginButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Botón de login clickeado');
                handleLogin(e);
                return false;
            });
        }

        // También configurar el evento submit del formulario
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Formulario de login enviado');
            handleLogin(e);
            return false;
        });
    } else {
        console.warn('Formulario de login no encontrado');
    }

    if (registerForm) {
        console.log('Formulario de registro encontrado, configurando evento...');

        // Configurar el botón de submit directamente
        const registerButton = registerForm.querySelector('button[type="submit"]');
        if (registerButton) {
            registerButton.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Botón de registro clickeado');
                handleRegister(e);
                return false;
            });
        }

        // También configurar el evento submit del formulario
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Formulario de registro enviado');
            handleRegister(e);
            return false;
        });
    } else {
        console.warn('Formulario de registro no encontrado');
    }

    console.log('Inicialización de autenticación completada');
});

// Verificar si el usuario está autenticado
async function checkAuth() {
    try {
        if (!supabaseClient) {
            console.error('Cliente Supabase no inicializado');
            return null;
        }

        const { data: { user } } = await supabaseClient.auth.getUser();

        // Obtener la ruta actual
        const currentPath = window.location.pathname;
        const isAuthPage = currentPath.includes('login.html') || currentPath.includes('register.html');
        const isDashboardPage = currentPath.includes('dashboard.html');

        if (user) {
            // Usuario autenticado
            console.log('Usuario autenticado:', user.email);

            // Si estamos en la página de login o registro, redirigir al dashboard
            if (isAuthPage) {
                console.log('Redirigiendo a dashboard...');
                window.location.href = 'dashboard.html';
            }
        } else {
            // Usuario no autenticado
            console.log('Usuario no autenticado');

            // Si estamos en el dashboard, redirigir al login
            if (isDashboardPage) {
                console.log('Redirigiendo a login...');
                window.location.href = 'login.html';
            }
        }

        return user;
    } catch (error) {
        console.error('Error al verificar autenticación:', error);

        // En caso de error, si estamos en el dashboard, redirigir al login
        if (window.location.pathname.includes('dashboard.html')) {
            window.location.href = 'login.html';
        }

        return null;
    }
}

// Manejar el inicio de sesión
async function handleLogin(e) {
    if (e && e.preventDefault) {
        e.preventDefault();
    }
    console.log('Ejecutando handleLogin...');

    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const errorMessage = document.getElementById('error-message');
    const submitButton = document.querySelector('.auth-button');

    // Deshabilitar el botón durante el proceso
    submitButton.disabled = true;
    submitButton.textContent = 'Iniciando sesión...';

    try {
        // Limpiar mensajes de error previos
        errorMessage.style.display = 'none';

        // Validaciones básicas
        if (!email || !password) {
            throw new Error('Por favor, completa todos los campos');
        }

        const { data, error } = await supabaseClient.auth.signInWithPassword({
            email,
            password
        });

        if (error) throw error;

        // Guardar información del usuario en localStorage
        localStorage.setItem('userEmail', email);

        // Mostrar mensaje de éxito
        errorMessage.textContent = '¡Inicio de sesión exitoso!';
        errorMessage.classList.add('success');
        errorMessage.style.display = 'block';

        // Redirigir al dashboard después de un breve momento
        setTimeout(() => {
            window.location.href = 'dashboard.html';
        }, 1000);
    } catch (error) {
        console.error('Error al iniciar sesión:', error);

        // Mostrar mensaje de error amigable
        let mensajeError = 'Error al iniciar sesión';

        if (error.message.includes('Invalid login credentials')) {
            mensajeError = 'Correo electrónico o contraseña incorrectos';
        } else if (error.message.includes('Email not confirmed')) {
            mensajeError = 'Por favor, confirma tu correo electrónico antes de iniciar sesión';
        } else {
            mensajeError += ': ' + error.message;
        }

        errorMessage.textContent = mensajeError;
        errorMessage.classList.remove('success');
        errorMessage.style.display = 'block';
    } finally {
        // Restaurar el botón
        submitButton.disabled = false;
        submitButton.textContent = 'Iniciar Sesión';
    }
}

// Manejar el registro
async function handleRegister(e) {
    if (e && e.preventDefault) {
        e.preventDefault();
    }
    console.log('Ejecutando handleRegister...');

    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm-password').value;
    const terms = document.getElementById('terms').checked;
    const errorMessage = document.getElementById('error-message');
    const submitButton = document.querySelector('.auth-button');

    // Deshabilitar el botón durante el proceso
    submitButton.disabled = true;
    submitButton.textContent = 'Creando cuenta...';

    // Limpiar mensajes de error previos
    errorMessage.style.display = 'none';
    errorMessage.classList.remove('success');

    // Validaciones
    try {
        if (!name || !email || !password || !confirmPassword) {
            throw new Error('Por favor, completa todos los campos');
        }

        if (password.length < 6) {
            throw new Error('La contraseña debe tener al menos 6 caracteres');
        }

        if (password !== confirmPassword) {
            throw new Error('Las contraseñas no coinciden');
        }

        if (!terms) {
            throw new Error('Debes aceptar los términos y condiciones');
        }

        // Verificar si el usuario viene de Hotmart (premium)
        const urlParams = new URLSearchParams(window.location.search);
        const isPremium = urlParams.get('premium') === 'true';

        // Guardar información del usuario en localStorage
        localStorage.setItem('userEmail', email);
        localStorage.setItem('userName', name);

        // Registrar usuario en Supabase
        console.log('Intentando registrar usuario con:', { email, name, isPremium });

        // Llamar a la función registrarUsuario definida en auth_functions.js
        let result;
        try {
            result = await registrarUsuario(email, password, name, isPremium);
        } catch (error) {
            console.error('Error al registrar usuario:', error);
            throw error;
        }

        if (result.method === 'otp') {
            // Si se usó el método OTP (enlace mágico)
            errorMessage.textContent = 'Se ha enviado un enlace de acceso a tu correo electrónico. Por favor, revisa tu bandeja de entrada y haz clic en el enlace para completar el registro.';
            errorMessage.classList.add('success');
            errorMessage.style.display = 'block';
        } else {
            // Si se usó el método de contraseña
            if (isPremium) {
                errorMessage.textContent = '¡Registro exitoso! Tu cuenta premium ha sido activada.';
                errorMessage.classList.add('success');
                errorMessage.style.display = 'block';

                // Redirigir al dashboard después de 2 segundos
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 2000);
            } else {
                // Mostrar mensaje de éxito y redirigir
                errorMessage.textContent = '¡Registro exitoso!';
                errorMessage.classList.add('success');
                errorMessage.style.display = 'block';

                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            }
        }
    } catch (error) {
        console.error('Error al registrar:', error);

        // Mostrar mensaje de error amigable
        let mensajeError = 'Error al registrar';

        if (error.message.includes('already registered')) {
            mensajeError = 'Este correo electrónico ya está registrado';
        } else if (error.message.includes('password')) {
            mensajeError = 'La contraseña no cumple con los requisitos de seguridad';
        } else if (error.message.includes('Database error saving new user')) {
            mensajeError = 'Error al guardar el usuario en la base de datos. Es posible que el esquema de la base de datos no esté correctamente configurado.';
            console.error('Este error suele ocurrir cuando el trigger para crear el perfil de usuario no está funcionando correctamente.');
            console.error('Por favor, verifica que hayas ejecutado el esquema SQL en Supabase.');
        } else {
            mensajeError = error.message;
        }

        errorMessage.textContent = mensajeError;
        errorMessage.classList.remove('success');
        errorMessage.style.display = 'block';
    } finally {
        // Restaurar el botón
        submitButton.disabled = false;
        submitButton.textContent = 'Crear Cuenta';
    }
}
