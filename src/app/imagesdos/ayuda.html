<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ayuda - Flasti Images</title>
    <link rel="icon" type="image/svg+xml" href="images/logo.svg">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="responsive.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #fff;
            background: linear-gradient(135deg, #13111C 0%, #1E1B2E 100%);
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: #fff;
        }

        .logo img {
            height: 40px;
            margin-right: 10px;
        }

        .logo h1 {
            font-size: 24px;
            margin: 0;
        }

        .ai-text {
            color: #6e8efb;
        }

        .content {
            background-color: rgba(30, 27, 46, 0.5);
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-top: 80px;
        }

        h1 {
            color: #fff;
            margin-top: 0;
            margin-bottom: 30px;
            font-size: 32px;
            text-align: center;
        }

        h2 {
            color: #a855f7;
            margin-top: 40px;
            margin-bottom: 15px;
            font-size: 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 10px;
        }

        p {
            margin-bottom: 20px;
            font-size: 16px;
            line-height: 1.6;
        }

        ul, ol {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        li {
            margin-bottom: 10px;
        }

        .help-section {
            margin-bottom: 40px;
        }

        .tip {
            background-color: rgba(168, 85, 247, 0.1);
            border-left: 4px solid #a855f7;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }

        .tip-title {
            font-weight: bold;
            color: #a855f7;
            margin-bottom: 5px;
        }

        .back-button {
            display: inline-block;
            margin-top: 30px;
            padding: 12px 25px;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        footer {
            text-align: center;
            margin-top: 50px;
            padding: 20px 0;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        @media (max-width: 768px) {
            .content {
                padding: 20px;
                margin-top: 60px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 22px;
            }
        }

        /* Estilos para la navegación */
        nav {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(19, 17, 28, 0.9);
            backdrop-filter: blur(10px);
            z-index: 1000;
            padding: 15px 0;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
        }

        .nav-logo-img {
            width: 30px;
            height: 30px;
            margin-right: 10px;
        }

        .nav-logo h1 {
            font-size: 20px;
            margin: 0;
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html" class="logo">
                    <img src="images/logo.svg" alt="Flasti Images Logo" class="nav-logo-img">
                    <h1>Flasti <span class="ai-text">Images</span></h1>
                </a>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="content">
            <h1>Centro de Ayuda</h1>

            <div class="help-section">
                <h2>Primeros Pasos</h2>
                <p>Bienvenido a Flasti Images, tu herramienta de generación de imágenes con IA. Aquí te explicamos cómo empezar:</p>

                <ol>
                    <li><strong>Registro:</strong> Crea una cuenta gratuita para comenzar a generar imágenes.</li>
                    <li><strong>Describe tu imagen:</strong> En el generador, escribe una descripción detallada de la imagen que deseas crear.</li>
                    <li><strong>Genera:</strong> Haz clic en el botón "Generar" y espera unos segundos mientras nuestra IA crea tu imagen.</li>
                    <li><strong>Descarga o comparte:</strong> Una vez generada, puedes descargar la imagen o compartirla directamente.</li>
                </ol>

                <div class="tip">
                    <div class="tip-title">Consejo</div>
                    <p>Cuanto más detallada sea tu descripción, mejores resultados obtendrás. Incluye información sobre estilo, colores, ambiente y elementos específicos.</p>
                </div>
            </div>

            <div class="help-section">
                <h2>Cómo Escribir Descripciones Efectivas</h2>
                <p>La calidad de tus imágenes generadas depende en gran medida de la descripción que proporciones. Aquí hay algunos consejos para escribir descripciones efectivas:</p>

                <ul>
                    <li><strong>Sé específico:</strong> "Un paisaje de montaña" es demasiado general. "Un paisaje de montañas nevadas al atardecer con un lago cristalino en primer plano" dará mejores resultados.</li>
                    <li><strong>Menciona el estilo:</strong> Especifica si quieres un estilo realista, cartoon, acuarela, óleo, etc.</li>
                    <li><strong>Incluye detalles de iluminación:</strong> Menciona si la escena es diurna, nocturna, al atardecer, etc.</li>
                    <li><strong>Describe la composición:</strong> Indica qué elementos deben estar en primer plano, fondo, etc.</li>
                    <li><strong>Especifica colores:</strong> Menciona la paleta de colores que deseas para tu imagen.</li>
                </ul>
            </div>

            <div class="help-section">
                <h2>Planes y Límites</h2>
                <p>Flasti Images ofrece los siguientes planes:</p>

                <ul>
                    <li><strong>Plan Gratuito:</strong> Permite generar hasta 5 imágenes para probar el servicio.</li>
                    <li><strong>Plan Premium:</strong> Por un pago único de $5, obtienes generación ilimitada de imágenes, máxima calidad, almacenamiento en la nube y soporte prioritario.</li>
                </ul>

                <p>Para actualizar a Premium, haz clic en "Obtener Premium" en la página principal o en el botón "Actualizar a Premium" en tu panel de usuario.</p>
            </div>

            <div class="help-section">
                <h2>Solución de Problemas Comunes</h2>

                <h3>La imagen no se genera</h3>
                <p>Si la imagen no se genera después de varios segundos, prueba lo siguiente:</p>
                <ul>
                    <li>Verifica tu conexión a internet</li>
                    <li>Recarga la página</li>
                    <li>Intenta con una descripción más corta o diferente</li>
                    <li>Borra la caché del navegador</li>
                </ul>

                <h3>La imagen no es lo que esperaba</h3>
                <p>Si la imagen generada no coincide con tu descripción:</p>
                <ul>
                    <li>Intenta ser más específico en tu descripción</li>
                    <li>Añade más detalles sobre el estilo, colores y composición</li>
                    <li>Evita descripciones contradictorias</li>
                </ul>

                <h3>Problemas con el pago</h3>
                <p>Si tienes problemas al realizar el pago:</p>
                <ul>
                    <li>Verifica que los datos de tu tarjeta sean correctos</li>
                    <li>Asegúrate de que tu tarjeta tenga fondos suficientes</li>
                    <li>Intenta con otro método de pago</li>
                    <li>Contacta a nuestro <NAME_EMAIL></li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="index.html" class="back-button">Volver al Inicio</a>
            </div>
        </div>

        <footer>
            <p>© 2025 Flasti Images | Todos los derechos reservados.</p>
        </footer>
    </div>
</body>
</html>
