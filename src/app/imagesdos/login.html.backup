<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Iniciar Sesión - Flasti AI</title>
    <link rel="icon" type="image/svg+xml" href="images/logo.svg">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="auth.css">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <img src="images/logo.svg" alt="Flasti AI Logo" class="auth-logo">
                <h1>Flasti <span class="ai-text">AI</span></h1>
            </div>
            <h2>Iniciar Sesión</h2>
            <div id="error-message" class="error-message" style="display: none;"></div>
            <form id="login-form" onsubmit="return false;">
                <div class="form-group">
                    <label for="email">Correo Electrónico</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Contraseña</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="auth-button">Iniciar Sesión</button>
            </form>
            <p class="auth-link">¿No tienes una cuenta? <a href="register.html">Regístrate</a></p>
            <p class="auth-link"><a href="index.html">Volver al inicio</a></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Verificar que Supabase se haya cargado correctamente
        window.addEventListener('load', function() {
            if (typeof supabase === 'undefined') {
                console.error('Error: Supabase no se ha cargado correctamente');
                alert('Error al cargar los recursos necesarios. Por favor, recarga la página.');
            } else {
                console.log('Supabase cargado correctamente');
            }
        });
    </script>
    <script src="auth_functions.js"></script>
    <script src="auth.js"></script>
</body>
</html>
