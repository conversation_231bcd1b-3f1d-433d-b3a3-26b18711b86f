"use client";

import React from 'react';

interface MercadoPagoLogoProps {
  className?: string;
}

const MercadoPagoLogo: React.FC<MercadoPagoLogoProps> = ({ className = "h-8 w-auto" }) => {
  return (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      viewBox="0 0 2000 1200" 
      className={className}
    >
      <path 
        d="M1262.7 528.7h-95.5c-7.2 0-13.4 5.2-14.6 12.3l-42.9 272.3c-.8 5.4 3.3 10.3 8.8 10.3h45.6c7.2 0 13.4-5.2 14.6-12.3l11.5-73.2c1.2-7.1 7.4-12.3 14.6-12.3h30.2c70.1 0 110.6-34 121.1-101.3 4.7-29.5-.8-52.7-15.9-68.9-16.7-17.8-46.5-26.9-77.5-26.9zm24.8 100.2c-5.8 38.1-35 38.1-63.2 38.1h-16.1l11.3-71.4c.7-4.3 4.4-7.5 8.7-7.5h7.4c19.2 0 37.3 0 46.7 11 5.6 6.5 7.3 16.2 5.2 29.8zm196.1-100.2h-95.7c-7.2 0-13.4 5.2-14.6 12.3l-42.9 272.3c-.8 5.4 3.3 10.3 8.8 10.3h45.7c7.2 0 13.4-5.2 14.6-12.3l11.5-73.2c1.2-7.1 7.4-12.3 14.6-12.3h30.2c70.1 0 110.6-34 121.1-101.3 4.7-29.5-.8-52.7-15.9-68.9-16.7-17.8-46.5-26.9-77.4-26.9zm24.8 100.2c-5.8 38.1-35 38.1-63.2 38.1h-16.1l11.3-71.4c.7-4.3 4.4-7.5 8.7-7.5h7.4c19.2 0 37.3 0 46.7 11 5.6 6.5 7.3 16.2 5.2 29.8zm196.1-100.2h-95.7c-7.2 0-13.4 5.2-14.6 12.3l-42.9 272.3c-.8 5.4 3.3 10.3 8.8 10.3h45.7c7.2 0 13.4-5.2 14.6-12.3l11.5-73.2c1.2-7.1 7.4-12.3 14.6-12.3h30.2c70.1 0 110.6-34 121.1-101.3 4.7-29.5-.8-52.7-15.9-68.9-16.7-17.8-46.5-26.9-77.4-26.9zm24.8 100.2c-5.8 38.1-35 38.1-63.2 38.1h-16.1l11.3-71.4c.7-4.3 4.4-7.5 8.7-7.5h7.4c19.2 0 37.3 0 46.7 11 5.6 6.5 7.3 16.2 5.2 29.8zm196.1-100.2h-95.7c-7.2 0-13.4 5.2-14.6 12.3l-42.9 272.3c-.8 5.4 3.3 10.3 8.8 10.3h45.7c7.2 0 13.4-5.2 14.6-12.3l11.5-73.2c1.2-7.1 7.4-12.3 14.6-12.3h30.2c70.1 0 110.6-34 121.1-101.3 4.7-29.5-.8-52.7-15.9-68.9-16.7-17.8-46.5-26.9-77.4-26.9zm24.8 100.2c-5.8 38.1-35 38.1-63.2 38.1h-16.1l11.3-71.4c.7-4.3 4.4-7.5 8.7-7.5h7.4c19.2 0 37.3 0 46.7 11 5.6 6.5 7.3 16.2 5.2 29.8z" 
        fill="#009ee3"
      />
      <path 
        d="M894.3 528.7H798c-7.2 0-13.4 5.2-14.6 12.3l-42.9 272.3c-.8 5.4 3.3 10.3 8.8 10.3h48.2c5.1 0 9.5-3.7 10.3-8.8l12.2-77.3c1.2-7.1 7.4-12.3 14.6-12.3h30.2c70.1 0 110.6-34 121.1-101.3 4.7-29.5-.8-52.7-15.9-68.9-16.7-17.8-46.5-26.9-77.5-26.9zm24.8 100.2c-5.8 38.1-35 38.1-63.2 38.1h-16.1l11.3-71.4c.7-4.3 4.4-7.5 8.7-7.5h7.4c19.2 0 37.3 0 46.7 11 5.6 6.5 7.3 16.2 5.2 29.8zm196.1-100.2h-95.7c-7.2 0-13.4 5.2-14.6 12.3l-42.9 272.3c-.8 5.4 3.3 10.3 8.8 10.3h45.7c7.2 0 13.4-5.2 14.6-12.3l11.5-73.2c1.2-7.1 7.4-12.3 14.6-12.3h30.2c70.1 0 110.6-34 121.1-101.3 4.7-29.5-.8-52.7-15.9-68.9-16.7-17.8-46.5-26.9-77.4-26.9zm24.8 100.2c-5.8 38.1-35 38.1-63.2 38.1h-16.1l11.3-71.4c.7-4.3 4.4-7.5 8.7-7.5h7.4c19.2 0 37.3 0 46.7 11 5.6 6.5 7.3 16.2 5.2 29.8z" 
        fill="#009ee3"
      />
      <path 
        d="M1998.3 629.7l-42.9 272.3c-.8 5.4 3.3 10.3 8.8 10.3h43.1c7.2 0 13.4-5.2 14.6-12.3l42.3-268.2c.8-5.4-3.3-10.3-8.8-10.3h-48.3c-4.3 0-8 3.2-8.8 7.5z" 
        fill="#009ee3"
      />
      <path 
        d="M543.5 733.3c-2.9 17.2-16.7 28.7-34.2 28.7-8.8 0-15.8-2.8-20.4-8.2-4.5-5.3-6.3-12.8-4.8-21.1 2.8-17 16.7-29 33.8-29 8.6 0 15.7 2.9 20.3 8.3 4.7 5.5 6.6 13.1 5.3 21.3zm66.5-92.2h-48.6c-4.2 0-7.7 3-8.3 7.1l-2.1 13.5-3.4-4.9c-10.5-15.3-34-20.4-57.4-20.4-53.7 0-99.6 40.7-108.6 97.7-4.6 28.4 1.9 55.5 18 74.4 14.8 17.4 35.9 24.6 61 24.6 43.1 0 67-27.7 67-27.7l-2.2 13.5c-.8 5.4 3.3 10.3 8.8 10.3h43.8c7.2 0 13.4-5.2 14.6-12.3l27.5-174.5c.9-5.4-3.2-10.3-8.7-10.3zm-385.8 92.2c-2.9 17.2-16.7 28.7-34.2 28.7-8.8 0-15.8-2.8-20.4-8.2-4.5-5.3-6.3-12.8-4.8-21.1 2.8-17 16.7-29 33.8-29 8.6 0 15.7 2.9 20.3 8.3 4.7 5.5 6.6 13.1 5.3 21.3zm66.5-92.2h-48.6c-4.2 0-7.7 3-8.3 7.1l-2.1 13.5-3.4-4.9c-10.5-15.3-34-20.4-57.4-20.4-53.7 0-99.6 40.7-108.6 97.7-4.6 28.4 1.9 55.5 18 74.4 14.8 17.4 35.9 24.6 61 24.6 43.1 0 67-27.7 67-27.7l-2.2 13.5c-.8 5.4 3.3 10.3 8.8 10.3h43.8c7.2 0 13.4-5.2 14.6-12.3l27.5-174.5c.9-5.4-3.2-10.3-8.7-10.3zm-203.1 0h-49c-4.7 0-9.1 2.3-11.7 6.2l-67.9 99.9-28.7-96c-1.7-5.7-7-9.6-13-9.6h-48.1c-5.6 0-9.5 5.5-7.7 10.8l54.2 159-50.9 71.9c-4 5.6.1 13.4 7 13.4h49c4.7 0 9-2.3 11.7-6.1l163.3-235.8c3.9-5.7-.2-13.7-7.2-13.7z" 
        fill="#009ee3"
      />
    </svg>
  );
};

export default MercadoPagoLogo;
