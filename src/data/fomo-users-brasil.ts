// Datos de usuarios ficticios de Brasil para notificaciones FOMO

import { FomoUser } from './fomo-users-types';

export const brasilUsers: FomoUser[] = [
  { firstName: "Miguel", lastName: "Silva", city: "São Paulo", region: "São Paulo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Alice", lastName: "Santos", city: "Rio de Janeiro", region: "Rio de Janeiro", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Arthur", lastName: "Oliveira", city: "Brasília", region: "Distrito Federal", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Sophia", lastName: "Souza", city: "Salvador", region: "Bahia", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Davi", lastName: "Rodrigues", city: "Fortaleza", region: "Ceará", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Helena", lastName: "Ferreira", city: "Belo Horizonte", region: "Minas Gerais", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Bernardo", lastName: "Almeida", city: "Manaus", region: "Amazonas", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Valentina", lastName: "Pereira", city: "Curitiba", region: "Paraná", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Heitor", lastName: "Nascimento", city: "Recife", region: "Pernambuco", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Laura", lastName: "Lima", city: "Porto Alegre", region: "Rio Grande do Sul", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Enzo", lastName: "Costa", city: "Belém", region: "Pará", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Maria Eduarda", lastName: "Carvalho", city: "Goiânia", region: "Goiás", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Pedro", lastName: "Gomes", city: "Guarulhos", region: "São Paulo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Manuela", lastName: "Ribeiro", city: "Campinas", region: "São Paulo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Gabriel", lastName: "Martins", city: "São Luís", region: "Maranhão", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Beatriz", lastName: "Araújo", city: "São Gonçalo", region: "Rio de Janeiro", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Samuel", lastName: "Lopes", city: "Maceió", region: "Alagoas", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Cecília", lastName: "Barbosa", city: "Duque de Caxias", region: "Rio de Janeiro", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Lorenzo", lastName: "Teixeira", city: "Natal", region: "Rio Grande do Norte", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Eloá", lastName: "Correia", city: "Campo Grande", region: "Mato Grosso do Sul", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Theo", lastName: "Fernandes", city: "Teresina", region: "Piauí", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Liz", lastName: "Vieira", city: "São Bernardo do Campo", region: "São Paulo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Guilherme", lastName: "Moreira", city: "João Pessoa", region: "Paraíba", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Maria Clara", lastName: "Dias", city: "Jaboatão dos Guararapes", region: "Pernambuco", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Isaac", lastName: "Castro", city: "Contagem", region: "Minas Gerais", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Lorena", lastName: "Cardoso", city: "Ribeirão Preto", region: "São Paulo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Benício", lastName: "Nunes", city: "Uberlândia", region: "Minas Gerais", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Antonella", lastName: "Monteiro", city: "Sorocaba", region: "São Paulo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Bryan", lastName: "Mendes", city: "Aracaju", region: "Sergipe", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Mariana", lastName: "Freitas", city: "Feira de Santana", region: "Bahia", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Felipe", lastName: "Pinto", city: "Cuiabá", region: "Mato Grosso", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Isis", lastName: "Cavalcanti", city: "Joinville", region: "Santa Catarina", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Davi Lucca", lastName: "Moura", city: "Londrina", region: "Paraná", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Lara", lastName: "Campos", city: "Niterói", region: "Rio de Janeiro", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Caleb", lastName: "Rocha", city: "Ananindeua", region: "Pará", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Yasmin", lastName: "Cunha", city: "São João de Meriti", region: "Rio de Janeiro", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Anthony", lastName: "Silveira", city: "Florianópolis", region: "Santa Catarina", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Melissa", lastName: "Macedo", city: "Santos", region: "São Paulo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Ravi", lastName: "Farias", city: "Vila Velha", region: "Espírito Santo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Laís", lastName: "Dantas", city: "Serra", region: "Espírito Santo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Henrique", lastName: "Medeiros", city: "Caxias do Sul", region: "Rio Grande do Sul", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Elisa", lastName: "Rezende", city: "Maringá", region: "Paraná", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Murilo", lastName: "Andrade", city: "Juiz de Fora", region: "Minas Gerais", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Marina", lastName: "Coelho", city: "Campina Grande", region: "Paraíba", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Vicente", lastName: "Barros", city: "Olinda", region: "Pernambuco", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Agatha", lastName: "Nogueira", city: "Caruaru", region: "Pernambuco", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Levi", lastName: "Borges", city: "Vitória", region: "Espírito Santo", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Catarina", lastName: "Santana", city: "Petrolina", region: "Pernambuco", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Noah", lastName: "Braga", city: "Imperatriz", region: "Maranhão", country: "Brasil", countryCode: "BR", flag: "🇧🇷" },
  { firstName: "Olívia", lastName: "Melo", city: "Camaçari", region: "Bahia", country: "Brasil", countryCode: "BR", flag: "🇧🇷" }
];
