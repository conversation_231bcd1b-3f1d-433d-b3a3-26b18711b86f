// Datos de usuarios ficticios de Colombia para notificaciones FOMO

import { FomoUser } from './fomo-users-types';

export const colombiaUsers: FomoUser[] = [
  { firstName: "Juan", lastName: "Rodríguez", city: "Bogotá", region: "Cundinamarca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Valentina", lastName: "Gómez", city: "Medellín", region: "Antioquia", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Santiago", lastName: "Hernández", city: "Cali", region: "Valle del Cauca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Mariana", lastName: "López", city: "Barranquilla", region: "Atlántico", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Matías", lastName: "Martínez", city: "Cartagena", region: "Bolívar", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Sofía", lastName: "Díaz", city: "Bucaramanga", region: "Santander", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Samuel", lastName: "Pérez", city: "Santa Marta", region: "Magdalena", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Isabella", lastName: "García", city: "Pereira", region: "Risaralda", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Emiliano", lastName: "Sánchez", city: "Manizales", region: "Caldas", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Luciana", lastName: "Ramírez", city: "Pasto", region: "Nariño", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Jerónimo", lastName: "Torres", city: "Ibagué", region: "Tolima", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Salomé", lastName: "Morales", city: "Cúcuta", region: "Norte de Santander", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Emmanuel", lastName: "Ortiz", city: "Villavicencio", region: "Meta", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Antonella", lastName: "Vargas", city: "Neiva", region: "Huila", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Tomás", lastName: "Gutiérrez", city: "Popayán", region: "Cauca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Gabriela", lastName: "Jiménez", city: "Armenia", region: "Quindío", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Sebastián", lastName: "Castro", city: "Montería", region: "Córdoba", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Valeria", lastName: "Reyes", city: "Sincelejo", region: "Sucre", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Nicolás", lastName: "Rojas", city: "Tunja", region: "Boyacá", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Sara", lastName: "Quintero", city: "Valledupar", region: "Cesar", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Jacobo", lastName: "Arias", city: "Riohacha", region: "La Guajira", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Julieta", lastName: "Cardona", city: "Quibdó", region: "Chocó", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Daniel", lastName: "Ospina", city: "Florencia", region: "Caquetá", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Lucía", lastName: "Duque", city: "Yopal", region: "Casanare", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Martín", lastName: "Mejía", city: "Mocoa", region: "Putumayo", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Antonia", lastName: "Parra", city: "Arauca", region: "Arauca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Esteban", lastName: "Correa", city: "Envigado", region: "Antioquia", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Abril", lastName: "Zuluaga", city: "Bello", region: "Antioquia", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Simón", lastName: "Echeverri", city: "Itagüí", region: "Antioquia", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Samantha", lastName: "Botero", city: "Palmira", region: "Valle del Cauca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Maximiliano", lastName: "Vélez", city: "Buenaventura", region: "Valle del Cauca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Danna", lastName: "Giraldo", city: "Soledad", region: "Atlántico", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Alejandro", lastName: "Jaramillo", city: "Soacha", region: "Cundinamarca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Paulina", lastName: "Londoño", city: "Facatativá", region: "Cundinamarca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Andrés", lastName: "Castaño", city: "Zipaquirá", region: "Cundinamarca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Camila", lastName: "Escobar", city: "Girardot", region: "Cundinamarca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Felipe", lastName: "Hoyos", city: "Dosquebradas", region: "Risaralda", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Silvana", lastName: "Agudelo", city: "Cartago", region: "Valle del Cauca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Julián", lastName: "Salazar", city: "Tuluá", region: "Valle del Cauca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Renata", lastName: "Orozco", city: "Barrancabermeja", region: "Santander", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Mateo", lastName: "Aristizábal", city: "Floridablanca", region: "Santander", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Aitana", lastName: "Carvajal", city: "Piedecuesta", region: "Santander", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "David", lastName: "Naranjo", city: "Maicao", region: "La Guajira", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Emilia", lastName: "Posada", city: "Ciénaga", region: "Magdalena", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Cristóbal", lastName: "Toro", city: "Lorica", region: "Córdoba", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Martina", lastName: "Uribe", city: "Magangué", region: "Bolívar", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Joaquín", lastName: "Gaviria", city: "Turbo", region: "Antioquia", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Catalina", lastName: "Zapata", city: "Apartadó", region: "Antioquia", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Leonardo", lastName: "Arango", city: "Fusagasugá", region: "Cundinamarca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" },
  { firstName: "Manuela", lastName: "Henao", city: "Chía", region: "Cundinamarca", country: "Colombia", countryCode: "CO", flag: "🇨🇴" }
];
