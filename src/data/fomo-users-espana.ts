// Datos de usuarios ficticios de España para notificaciones FOMO

import { FomoUser } from './fomo-users-types';

export const espanaUsers: FomoUser[] = [
  { firstName: "Alejandro", lastName: "García", city: "Madrid", region: "Comunidad de Madrid", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Lucía", lastName: "Fernández", city: "Barcelona", region: "Cataluña", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "<PERSON>", lastName: "Martínez", city: "Valencia", region: "Comunidad Valenciana", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Sofía", lastName: "López", city: "Sevilla", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Martín", lastName: "González", city: "Zaragoza", region: "Aragón", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "María", lastName: "Rodríguez", city: "Málaga", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Pablo", lastName: "Sánchez", city: "Murcia", region: "Región de Murcia", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Daniela", lastName: "Pérez", city: "Palma", region: "Islas Baleares", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Leo", lastName: "Díaz", city: "Bilbao", region: "País Vasco", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Carla", lastName: "Ruiz", city: "Alicante", region: "Comunidad Valenciana", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Daniel", lastName: "Moreno", city: "Córdoba", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Noa", lastName: "Jiménez", city: "Valladolid", region: "Castilla y León", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Mateo", lastName: "Hernández", city: "Vigo", region: "Galicia", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Martina", lastName: "Gómez", city: "Gijón", region: "Asturias", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Álvaro", lastName: "Navarro", city: "A Coruña", region: "Galicia", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Claudia", lastName: "Torres", city: "Granada", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Manuel", lastName: "Domínguez", city: "Vitoria", region: "País Vasco", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Alma", lastName: "Vázquez", city: "Elche", region: "Comunidad Valenciana", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Adrián", lastName: "Ramos", city: "Oviedo", region: "Asturias", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Julia", lastName: "Gil", city: "Cartagena", region: "Región de Murcia", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "David", lastName: "Serrano", city: "Jerez de la Frontera", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Vera", lastName: "Gutiérrez", city: "Sabadell", region: "Cataluña", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Mario", lastName: "Ortega", city: "Móstoles", region: "Comunidad de Madrid", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Valeria", lastName: "Marín", city: "Alcalá de Henares", region: "Comunidad de Madrid", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Marcos", lastName: "Romero", city: "Pamplona", region: "Navarra", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Laia", lastName: "Alonso", city: "Fuenlabrada", region: "Comunidad de Madrid", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Javier", lastName: "Gutiérrez", city: "Almería", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Lola", lastName: "Muñoz", city: "Leganés", region: "Comunidad de Madrid", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Iker", lastName: "Sanz", city: "San Sebastián", region: "País Vasco", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Candela", lastName: "Iglesias", city: "Santander", region: "Cantabria", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Aitor", lastName: "Castillo", city: "Burgos", region: "Castilla y León", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Olivia", lastName: "Medina", city: "Albacete", region: "Castilla-La Mancha", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Unai", lastName: "Cortes", city: "Castellón", region: "Comunidad Valenciana", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Aitana", lastName: "Herrera", city: "Logroño", region: "La Rioja", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Izan", lastName: "Garrido", city: "Badajoz", region: "Extremadura", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Vega", lastName: "Núñez", city: "Salamanca", region: "Castilla y León", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Héctor", lastName: "Velasco", city: "Huelva", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Abril", lastName: "Cano", city: "Tarragona", region: "Cataluña", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Nil", lastName: "Prieto", city: "Lleida", region: "Cataluña", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Triana", lastName: "Méndez", city: "Cádiz", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Bruno", lastName: "Calvo", city: "León", region: "Castilla y León", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Chloe", lastName: "Gallego", city: "Marbella", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Marc", lastName: "Reyes", city: "Girona", region: "Cataluña", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Nora", lastName: "Cabrera", city: "Terrassa", region: "Cataluña", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Pol", lastName: "Blanco", city: "Alcorcón", region: "Comunidad de Madrid", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Gala", lastName: "Fuentes", city: "Jaén", region: "Andalucía", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Asier", lastName: "Carrasco", city: "Ourense", region: "Galicia", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Ona", lastName: "Martí", city: "Reus", region: "Cataluña", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Biel", lastName: "Santos", city: "Torrevieja", region: "Comunidad Valenciana", country: "España", countryCode: "ES", flag: "🇪🇸" },
  { firstName: "Mara", lastName: "Ferrer", city: "Parla", region: "Comunidad de Madrid", country: "España", countryCode: "ES", flag: "🇪🇸" }
];
