// Datos de usuarios ficticios de México para notificaciones FOMO

import { FomoUser } from './fomo-users-types';

export const mexicoUsers: FomoUser[] = [
  { firstName: "Luis", lastName: "Hernández", city: "Ciudad de México", region: "CDMX", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Sofía", lastName: "García", city: "Guadalajara", region: "Jalisco", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Diego", lastName: "Martínez", city: "Monterrey", region: "Nuevo León", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Valentina", lastName: "López", city: "Puebla", region: "Puebla", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Mateo", lastName: "González", city: "Tijuana", region: "Baja California", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Regina", lastName: "Rodríguez", city: "León", region: "Guanajuato", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Santiago", lastName: "Pérez", city: "Querétaro", region: "Querétaro", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Camila", lastName: "Sánchez", city: "Mérida", region: "Yucatán", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Emiliano", lastName: "Flores", city: "Cancún", region: "Quintana Roo", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Valeria", lastName: "Torres", city: "Veracruz", region: "Veracruz", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Leonardo", lastName: "Ramírez", city: "Toluca", region: "Estado de México", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Ximena", lastName: "Díaz", city: "Mexicali", region: "Baja California", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Sebastián", lastName: "Morales", city: "Aguascalientes", region: "Aguascalientes", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Renata", lastName: "Gutiérrez", city: "Cuernavaca", region: "Morelos", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Matías", lastName: "Ortiz", city: "Chihuahua", region: "Chihuahua", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Victoria", lastName: "Vargas", city: "Tampico", region: "Tamaulipas", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Daniel", lastName: "Castillo", city: "Saltillo", region: "Coahuila", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Natalia", lastName: "Reyes", city: "Hermosillo", region: "Sonora", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Alejandro", lastName: "Mendoza", city: "Culiacán", region: "Sinaloa", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Miranda", lastName: "Jiménez", city: "Morelia", region: "Michoacán", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Patricio", lastName: "Vázquez", city: "Torreón", region: "Coahuila", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Daniela", lastName: "Aguilar", city: "San Luis Potosí", region: "San Luis Potosí", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Emilio", lastName: "Romero", city: "Acapulco", region: "Guerrero", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Mariana", lastName: "Navarro", city: "Villahermosa", region: "Tabasco", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Maximiliano", lastName: "Rivas", city: "Durango", region: "Durango", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Fernanda", lastName: "Medina", city: "Tuxtla Gutiérrez", region: "Chiapas", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Gael", lastName: "Delgado", city: "Mazatlán", region: "Sinaloa", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Alexa", lastName: "Fuentes", city: "Tepic", region: "Nayarit", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Iker", lastName: "Ríos", city: "Xalapa", region: "Veracruz", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Danna", lastName: "Soto", city: "Oaxaca", region: "Oaxaca", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Bruno", lastName: "Lara", city: "Ciudad Juárez", region: "Chihuahua", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Allison", lastName: "Cortés", city: "Ensenada", region: "Baja California", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Javier", lastName: "Núñez", city: "Irapuato", region: "Guanajuato", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Mía", lastName: "Campos", city: "Celaya", region: "Guanajuato", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Carlos", lastName: "Acosta", city: "Pachuca", region: "Hidalgo", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Samantha", lastName: "Ochoa", city: "Campeche", region: "Campeche", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Ángel", lastName: "Cabrera", city: "Chetumal", region: "Quintana Roo", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Melissa", lastName: "Velázquez", city: "Reynosa", region: "Tamaulipas", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Eduardo", lastName: "Cervantes", city: "Nuevo Laredo", region: "Tamaulipas", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Valentina", lastName: "Guerrero", city: "Matamoros", region: "Tamaulipas", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Andrés", lastName: "Espinoza", city: "Los Cabos", region: "Baja California Sur", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Fátima", lastName: "Contreras", city: "La Paz", region: "Baja California Sur", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Ricardo", lastName: "Padilla", city: "Uruapan", region: "Michoacán", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Montserrat", lastName: "Arellano", city: "Colima", region: "Colima", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Mauricio", lastName: "Zamora", city: "Zacatecas", region: "Zacatecas", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Paulina", lastName: "Barrera", city: "Playa del Carmen", region: "Quintana Roo", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Josué", lastName: "Gallegos", city: "Puerto Vallarta", region: "Jalisco", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Aitana", lastName: "Ponce", city: "Tapachula", region: "Chiapas", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Rodrigo", lastName: "Robles", city: "Coatzacoalcos", region: "Veracruz", country: "México", countryCode: "MX", flag: "🇲🇽" },
  { firstName: "Ivanna", lastName: "Cárdenas", city: "Tlaxcala", region: "Tlaxcala", country: "México", countryCode: "MX", flag: "🇲🇽" }
];
