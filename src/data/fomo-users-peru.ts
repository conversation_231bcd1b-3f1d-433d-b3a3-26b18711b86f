// Datos de usuarios ficticios de Perú para notificaciones FOMO

import { FomoUser } from './fomo-users-types';

export const peruUsers: FomoUser[] = [
  { firstName: "Matías", lastName: "García", city: "Lima", region: "Lima", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Luciana", lastName: "Rodríguez", city: "Arequipa", region: "Arequipa", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Sebastián", lastName: "Flores", city: "Trujillo", region: "La Libertad", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Valentina", lastName: "Martínez", city: "Cusco", region: "Cusco", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Joaquín", lastName: "<PERSON>", city: "Chiclayo", region: "Lambayeque", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Camila", lastName: "Torres", city: "Piura", region: "Piura", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Benjamín", lastName: "Díaz", city: "Iquitos", region: "Loreto", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Sofía", lastName: "Sánchez", city: "Huancayo", region: "Junín", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Thiago", lastName: "Vargas", city: "Tacna", region: "Tacna", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Antonella", lastName: "Mendoza", city: "Pucallpa", region: "Ucayali", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Santiago", lastName: "Castillo", city: "Chimbote", region: "Áncash", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Valeria", lastName: "Gutiérrez", city: "Ica", region: "Ica", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Nicolás", lastName: "Ramos", city: "Juliaca", region: "Puno", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Micaela", lastName: "Rojas", city: "Sullana", region: "Piura", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Facundo", lastName: "Chávez", city: "Ayacucho", region: "Ayacucho", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Catalina", lastName: "Quispe", city: "Cajamarca", region: "Cajamarca", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Emiliano", lastName: "Huamán", city: "Puno", region: "Puno", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Renata", lastName: "Mamani", city: "Tarapoto", region: "San Martín", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Dante", lastName: "Condori", city: "Tumbes", region: "Tumbes", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Mía", lastName: "Paredes", city: "Huánuco", region: "Huánuco", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Lautaro", lastName: "Vega", city: "Moquegua", region: "Moquegua", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Samantha", lastName: "Aguilar", city: "Chincha Alta", region: "Ica", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Bautista", lastName: "Medina", city: "Huaraz", region: "Áncash", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Abril", lastName: "Yupanqui", city: "Abancay", region: "Apurímac", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Máximo", lastName: "Palacios", city: "Moyobamba", region: "San Martín", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Delfina", lastName: "Herrera", city: "Cerro de Pasco", region: "Pasco", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Santino", lastName: "Cáceres", city: "Talara", region: "Piura", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Martina", lastName: "Reyes", city: "Huacho", region: "Lima", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Bastián", lastName: "Morales", city: "Jaén", region: "Cajamarca", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Agustina", lastName: "Campos", city: "Tarma", region: "Junín", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Salvador", lastName: "Ríos", city: "Yurimaguas", region: "Loreto", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Emilia", lastName: "Espinoza", city: "Chosica", region: "Lima", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Ignacio", lastName: "Córdova", city: "Sicuani", region: "Cusco", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Olivia", lastName: "Delgado", city: "Tingo María", region: "Huánuco", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Tomás", lastName: "Ortega", city: "Chulucanas", region: "Piura", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Julieta", lastName: "Navarro", city: "Ilo", region: "Moquegua", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Bruno", lastName: "Valdivia", city: "Andahuaylas", region: "Apurímac", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Victoria", lastName: "Zegarra", city: "Chancay", region: "Lima", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Mateo", lastName: "Pacheco", city: "Huanta", region: "Ayacucho", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Isabella", lastName: "Salazar", city: "Barranca", region: "Lima", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Felipe", lastName: "Arce", city: "Chepén", region: "La Libertad", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Constanza", lastName: "Velásquez", city: "Ferreñafe", region: "Lambayeque", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Alonso", lastName: "Cabrera", city: "Requena", region: "Loreto", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Amelia", lastName: "Noriega", city: "Satipo", region: "Junín", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Cristóbal", lastName: "Hidalgo", city: "Oxapampa", region: "Pasco", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Isidora", lastName: "Benavides", city: "Chachapoyas", region: "Amazonas", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Simón", lastName: "Urbina", city: "Nazca", region: "Ica", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Amanda", lastName: "Palomino", city: "Huarmey", region: "Áncash", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Joaquín", lastName: "Tapia", city: "Bagua Grande", region: "Amazonas", country: "Perú", countryCode: "PE", flag: "🇵🇪" },
  { firstName: "Maite", lastName: "Villanueva", city: "Huancavelica", region: "Huancavelica", country: "Perú", countryCode: "PE", flag: "🇵🇪" }
];
