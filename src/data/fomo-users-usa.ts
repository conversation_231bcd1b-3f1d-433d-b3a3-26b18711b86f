// Datos de usuarios ficticios de Estados Unidos para notificaciones FOMO

import { FomoUser } from './fomo-users-types';

export const usaUsers: FomoUser[] = [
  { firstName: "<PERSON>", lastName: "Smith", city: "New York", region: "New York", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Emma", lastName: "Johnson", city: "Los Angeles", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "<PERSON>", lastName: "Williams", city: "Chicago", region: "Illinois", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Olivia", lastName: "Brown", city: "Houston", region: "Texas", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "<PERSON>", lastName: "<PERSON>", city: "Phoenix", region: "Arizona", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Ava", lastName: "Garcia", city: "Philadelphia", region: "Pennsylvania", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "James", lastName: "Miller", city: "San Antonio", region: "Texas", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Sophia", lastName: "Davis", city: "San Diego", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Benjamin", lastName: "Rodriguez", city: "Dallas", region: "Texas", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Isabella", lastName: "Martinez", city: "San Jose", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Lucas", lastName: "Hernandez", city: "Austin", region: "Texas", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Mia", lastName: "Lopez", city: "Jacksonville", region: "Florida", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Mason", lastName: "Gonzalez", city: "Fort Worth", region: "Texas", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Charlotte", lastName: "Wilson", city: "Columbus", region: "Ohio", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Ethan", lastName: "Anderson", city: "Indianapolis", region: "Indiana", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Amelia", lastName: "Thomas", city: "Charlotte", region: "North Carolina", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Henry", lastName: "Taylor", city: "Seattle", region: "Washington", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Harper", lastName: "Moore", city: "Denver", region: "Colorado", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Alexander", lastName: "Jackson", city: "Boston", region: "Massachusetts", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Evelyn", lastName: "Martin", city: "Nashville", region: "Tennessee", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Michael", lastName: "Lee", city: "Portland", region: "Oregon", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Abigail", lastName: "Perez", city: "Las Vegas", region: "Nevada", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Daniel", lastName: "Thompson", city: "Detroit", region: "Michigan", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Emily", lastName: "White", city: "Memphis", region: "Tennessee", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Matthew", lastName: "Harris", city: "Louisville", region: "Kentucky", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Elizabeth", lastName: "Sanchez", city: "Baltimore", region: "Maryland", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Jackson", lastName: "Clark", city: "Milwaukee", region: "Wisconsin", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Sofia", lastName: "Lewis", city: "Albuquerque", region: "New Mexico", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Sebastian", lastName: "Robinson", city: "Tucson", region: "Arizona", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Avery", lastName: "Walker", city: "Fresno", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "David", lastName: "Young", city: "Sacramento", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Scarlett", lastName: "Allen", city: "Kansas City", region: "Missouri", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Joseph", lastName: "King", city: "Mesa", region: "Arizona", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Victoria", lastName: "Wright", city: "Atlanta", region: "Georgia", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Carter", lastName: "Scott", city: "Omaha", region: "Nebraska", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Madison", lastName: "Torres", city: "Raleigh", region: "North Carolina", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "John", lastName: "Nguyen", city: "Miami", region: "Florida", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Luna", lastName: "Hill", city: "Oakland", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Owen", lastName: "Flores", city: "Minneapolis", region: "Minnesota", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Grace", lastName: "Green", city: "Tulsa", region: "Oklahoma", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Wyatt", lastName: "Adams", city: "Cleveland", region: "Ohio", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Chloe", lastName: "Nelson", city: "Wichita", region: "Kansas", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Samuel", lastName: "Baker", city: "Arlington", region: "Texas", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Lily", lastName: "Hall", city: "New Orleans", region: "Louisiana", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Aiden", lastName: "Rivera", city: "Bakersfield", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Zoe", lastName: "Campbell", city: "Tampa", region: "Florida", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Luke", lastName: "Mitchell", city: "Honolulu", region: "Hawaii", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Penelope", lastName: "Carter", city: "Aurora", region: "Colorado", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Gabriel", lastName: "Roberts", city: "Anaheim", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" },
  { firstName: "Layla", lastName: "Gomez", city: "Santa Ana", region: "California", country: "Estados Unidos", countryCode: "US", flag: "🇺🇸" }
];
