import nodemailer from 'nodemailer';

// Configuración del transportador de email para Zoho
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: 'smtp.zoho.com',
    port: 587,
    secure: false, // true para 465, false para otros puertos
    auth: {
      user: process.env.ZOHO_EMAIL || '<EMAIL>',
      pass: process.env.ZOHO_APP_PASSWORD || '8A0D5LexU8hB'
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

// Template HTML para el email de bienvenida
const getWelcomeEmailTemplate = (userName: string) => {
  return `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bienvenido a Flasti</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #0f0f1a;
            color: #ffffff;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 40px 30px;
            text-align: center;
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 10px;
        }
        .header-subtitle {
            color: #e0e7ff;
            font-size: 16px;
            margin: 0;
        }
        .content {
            padding: 40px 30px;
        }
        .welcome-title {
            font-size: 28px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 20px;
            text-align: center;
        }
        .welcome-message {
            font-size: 16px;
            line-height: 1.6;
            color: #cbd5e1;
            margin-bottom: 30px;
            text-align: center;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #ffffff;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            text-align: center;
            margin: 20px auto;
            display: block;
            width: fit-content;
            transition: transform 0.2s ease;
        }
        .cta-button:hover {
            transform: translateY(-2px);
        }
        .features {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
        }
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .feature-icon {
            width: 24px;
            height: 24px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 12px;
        }
        .footer {
            background: rgba(0, 0, 0, 0.2);
            padding: 30px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .footer-text {
            color: #94a3b8;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-link {
            display: inline-block;
            margin: 0 10px;
            color: #667eea;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">💎 FLASTI</div>
            <p class="header-subtitle">Plataforma de Afiliados Premium</p>
        </div>
        
        <div class="content">
            <h1 class="welcome-title">¡Hola ${userName}! 👋</h1>
            
            <p class="welcome-message">
                ¡Te damos la más cordial bienvenida a Flasti! Estamos emocionados de tenerte como parte de nuestra comunidad de afiliados exitosos.
            </p>
            
            <p class="welcome-message">
                Tu pago ha sido procesado exitosamente y tu cuenta está lista para comenzar a generar ingresos.
            </p>
            
            <a href="https://flasti.com/secure-registration-portal-7f9a2b3c5d8e" class="cta-button">
                🚀 Acceder a mi cuenta
            </a>
            
            <div class="features">
                <div class="feature-item">
                    <div class="feature-icon">💰</div>
                    <div>
                        <strong>Comisiones altas:</strong> Gana hasta 70% de comisión en cada venta
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <div>
                        <strong>Dashboard completo:</strong> Monitorea tus ganancias en tiempo real
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎯</div>
                    <div>
                        <strong>Herramientas premium:</strong> Acceso a materiales de marketing exclusivos
                    </div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💎</div>
                    <div>
                        <strong>Soporte VIP:</strong> Asistencia personalizada para maximizar tus ganancias
                    </div>
                </div>
            </div>
            
            <p class="welcome-message">
                <strong>Próximos pasos:</strong><br>
                1. Accede a tu dashboard usando el botón de arriba<br>
                2. Completa tu perfil de afiliado<br>
                3. Obtén tus enlaces únicos de afiliado<br>
                4. ¡Comienza a ganar dinero!
            </p>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Si tienes alguna pregunta, no dudes en contactarnos.<br>
                Estamos aquí para ayudarte a tener éxito.
            </p>
            
            <div class="social-links">
                <a href="mailto:<EMAIL>" class="social-link">📧 Soporte</a>
                <a href="https://flasti.com" class="social-link">🌐 Sitio Web</a>
            </div>
            
            <p class="footer-text">
                © 2024 Flasti. Todos los derechos reservados.<br>
                <small>Este email fue enviado a tu dirección porque completaste una compra en Flasti.</small>
            </p>
        </div>
    </div>
</body>
</html>
  `;
};

// Función para enviar email de bienvenida
export const sendWelcomeEmail = async (
  userEmail: string, 
  userName: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: {
        name: 'Flasti',
        address: process.env.ZOHO_EMAIL || '<EMAIL>'
      },
      to: userEmail,
      subject: `${userName} 👋 💎 ¡Te damos la bienvenida a Flasti!`,
      html: getWelcomeEmailTemplate(userName),
      text: `¡Hola ${userName}! Te damos la bienvenida a Flasti. Tu pago ha sido procesado exitosamente y tu cuenta está lista. Accede a tu dashboard en: https://flasti.com/secure-registration-portal-7f9a2b3c5d8e`
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email de bienvenida enviado exitosamente:', result.messageId);
    
    return { success: true };
  } catch (error) {
    console.error('Error al enviar email de bienvenida:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Error desconocido' 
    };
  }
};

export default { sendWelcomeEmail };
