interface WelcomeEmailData {
  fullName: string;
  email: string;
  transactionId?: string;
  amount?: string;
}

export function getWelcomeEmailTemplate(data: WelcomeEmailData): string {
  const { fullName } = data;
  
  return `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bienvenido a Flasti</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #6B46C1 0%, #3B82F6 50%, #1E3A8A 100%);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
            position: relative;
        }
        
        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(107, 70, 193, 0.9) 0%, rgba(59, 130, 246, 0.8) 50%, rgba(30, 58, 138, 0.9) 100%);
            z-index: 1;
        }
        
        .content-wrapper {
            position: relative;
            z-index: 2;
        }
        
        .header {
            padding: 50px 40px 40px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        
        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #EC4899 0%, #3B82F6 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            box-shadow: 0 8px 32px rgba(236, 72, 153, 0.3);
        }
        
        .logo-text {
            font-size: 36px;
            font-weight: 800;
            color: #FFFFFF;
            letter-spacing: -0.02em;
        }
        
        .header-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            font-weight: 500;
            margin-top: 8px;
        }
        
        .main-content {
            padding: 50px 40px;
            background: rgba(255, 255, 255, 0.02);
        }
        
        .welcome-title {
            font-size: 32px;
            font-weight: 700;
            color: #FFFFFF;
            text-align: center;
            margin-bottom: 24px;
            line-height: 1.2;
        }
        
        .welcome-message {
            font-size: 18px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
            margin-bottom: 32px;
        }
        
        .cta-container {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #EC4899 0%, #F97316 100%);
            color: #FFFFFF;
            text-decoration: none;
            padding: 18px 36px;
            border-radius: 16px;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0.5px;
            box-shadow: 0 12px 24px rgba(236, 72, 153, 0.4);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            backdrop-filter: blur(10px);
        }
        
        .feature-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #EC4899 0%, #3B82F6 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 20px;
        }
        
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
        }
        
        .feature-description {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            line-height: 1.5;
        }
        
        .next-steps {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 32px;
            margin: 40px 0;
        }
        
        .next-steps-title {
            font-size: 20px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px 0;
        }
        
        .step-number {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #EC4899 0%, #3B82F6 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #FFFFFF;
            font-weight: 600;
            font-size: 14px;
            margin-right: 16px;
            flex-shrink: 0;
        }
        
        .step-text {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            line-height: 1.4;
        }
        
        .footer {
            background: rgba(0, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .footer-text {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 24px;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 32px;
            margin: 24px 0;
        }
        
        .contact-item {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .copyright {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
            margin-top: 24px;
        }
        
        /* Responsive Design */
        @media (max-width: 640px) {
            body {
                padding: 10px;
            }
            
            .header, .main-content, .footer {
                padding: 30px 24px;
            }
            
            .welcome-title {
                font-size: 28px;
            }
            
            .welcome-message {
                font-size: 16px;
            }
            
            .logo-text {
                font-size: 28px;
            }
            
            .contact-info {
                flex-direction: column;
                gap: 16px;
            }
            
            .features-grid {
                gap: 16px;
            }
            
            .feature-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="content-wrapper">
            <div class="header">
                <div class="logo-container">
                    <div class="logo-icon">F</div>
                    <div class="logo-text">flasti</div>
                </div>
                <p class="header-subtitle">Plataforma de Afiliados Premium</p>
            </div>
            
            <div class="main-content">
                <h1 class="welcome-title">¡Hola ${fullName}! 👋</h1>
                
                <p class="welcome-message">
                    ¡Te damos la más cordial bienvenida a Flasti! Estamos emocionados de tenerte como parte de nuestra comunidad de afiliados exitosos.
                </p>
                
                <p class="welcome-message">
                    Tu pago ha sido procesado exitosamente y tu cuenta está lista para comenzar a generar ingresos extraordinarios.
                </p>
                
                <div class="cta-container">
                    <a href="https://flasti.com/secure-registration-portal-7f9a2b3c5d8e" class="cta-button">
                        🚀 Acceder a mi Dashboard
                    </a>
                </div>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-header">
                            <div class="feature-icon">💰</div>
                            <div class="feature-title">Comisiones Premium</div>
                        </div>
                        <div class="feature-description">
                            Gana hasta 70% de comisión en cada venta con nuestro sistema de recompensas exclusivo
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-header">
                            <div class="feature-icon">📊</div>
                            <div class="feature-title">Analytics Avanzados</div>
                        </div>
                        <div class="feature-description">
                            Dashboard completo con métricas en tiempo real y herramientas de optimización
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-header">
                            <div class="feature-icon">🎯</div>
                            <div class="feature-title">Marketing Tools</div>
                        </div>
                        <div class="feature-description">
                            Acceso a materiales de marketing premium y campañas de alta conversión
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-header">
                            <div class="feature-icon">💎</div>
                            <div class="feature-title">Soporte VIP</div>
                        </div>
                        <div class="feature-description">
                            Asistencia personalizada 24/7 para maximizar tus ganancias y éxito
                        </div>
                    </div>
                </div>
                
                <div class="next-steps">
                    <h3 class="next-steps-title">🎯 Próximos Pasos</h3>
                    
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-text">Accede a tu dashboard premium usando el botón de arriba</div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-text">Completa tu perfil de afiliado y configura tus preferencias</div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-text">Obtén tus enlaces únicos de afiliado y materiales de marketing</div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-text">¡Comienza a ganar dinero y construye tu imperio digital!</div>
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <p class="footer-text">
                    Si tienes alguna pregunta o necesitas asistencia, nuestro equipo de soporte VIP está aquí para ayudarte a alcanzar el éxito.
                </p>
                
                <div class="contact-info">
                    <a href="mailto:<EMAIL>" class="contact-item">📧 <EMAIL></a>
                    <a href="https://flasti.com" class="contact-item">🌐 flasti.com</a>
                </div>
                
                <p class="copyright">
                    © 2024 Flasti. Todos los derechos reservados.<br>
                    <small>Este email fue enviado porque completaste exitosamente tu registro en Flasti.</small>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
  `;
}
