interface WelcomeEmailData {
  fullName: string;
  email: string;
  transactionId?: string;
  amount?: string;
}

export function getWelcomeEmailTemplate(data: WelcomeEmailData): string {
  const { fullName } = data;

  return `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bienvenido a Flasti</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .email-wrapper {
            width: 100%;
            background-color: #f8fafc;
            padding: 40px 20px;
        }

        .email-container {
            max-width: 680px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid #e2e8f0;
        }

        .header {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            padding: 48px 48px 32px;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.5;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            position: relative;
            z-index: 2;
        }

        .logo-image {
            width: 48px;
            height: 48px;
            margin-right: 16px;
            background: linear-gradient(135deg, #ec4899 0%, #f97316 50%, #3b82f6 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(236, 72, 153, 0.3);
        }

        .logo-text {
            font-size: 28px;
            font-weight: 700;
            color: #ffffff;
            letter-spacing: -0.5px;
        }

        .main-content {
            padding: 48px;
            background-color: #ffffff;
        }

        .greeting {
            font-size: 32px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 24px;
            line-height: 1.2;
        }

        .congratulations {
            font-size: 24px;
            font-weight: 600;
            color: #0f172a;
            margin-bottom: 32px;
            line-height: 1.3;
        }

        .main-message {
            font-size: 18px;
            line-height: 1.7;
            color: #475569;
            margin-bottom: 40px;
            font-weight: 400;
        }

        .access-section {
            text-align: center;
            margin: 48px 0;
            padding: 32px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 16px;
            border: 1px solid #e2e8f0;
        }

        .access-title {
            font-size: 20px;
            font-weight: 600;
            color: #334155;
            margin-bottom: 12px;
        }

        .access-subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 24px;
        }

        .welcome-banner {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            border-radius: 16px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .welcome-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(236, 72, 153, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
            border-radius: 16px;
        }

        .welcome-banner-content {
            position: relative;
            z-index: 2;
        }

        .welcome-banner-title {
            font-size: 28px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 20px;
            line-height: 1.2;
        }

        .welcome-banner-text {
            font-size: 18px;
            line-height: 1.6;
            color: #cbd5e1;
            margin-bottom: 32px;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
            color: #ffffff;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0.25px;
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
            transition: all 0.2s ease;
            border: none;
            text-transform: uppercase;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4);
        }

        .next-steps {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
            padding: 40px;
            margin: 40px 0;
        }

        .next-steps-title {
            font-size: 24px;
            font-weight: 700;
            color: #0f172a;
            margin-bottom: 24px;
        }

        .step-content {
            margin-bottom: 32px;
            padding: 24px;
            background-color: #ffffff;
            border-radius: 12px;
            border-left: 4px solid #3b82f6;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .step-title {
            font-size: 20px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .step-description {
            font-size: 16px;
            line-height: 1.7;
            color: #475569;
        }

        .support-section {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border-radius: 16px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .support-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        }

        .support-content {
            position: relative;
            z-index: 2;
        }

        .support-title {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 16px;
        }

        .support-subtitle {
            font-size: 18px;
            font-weight: 600;
            color: #cbd5e1;
            margin-bottom: 20px;
        }

        .support-text {
            font-size: 16px;
            line-height: 1.7;
            color: #94a3b8;
        }

        .footer {
            background-color: #f1f5f9;
            padding: 40px 48px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 32px;
            margin: 24px 0;
        }

        .contact-item {
            color: #475569;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            background-color: #ffffff;
            border: 1px solid #e2e8f0;
            transition: all 0.2s ease;
        }

        .contact-item:hover {
            background-color: #f8fafc;
            border-color: #cbd5e1;
        }

        .copyright {
            color: #64748b;
            font-size: 12px;
            margin-top: 24px;
            line-height: 1.5;
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            .email-wrapper {
                padding: 20px 10px;
            }

            .header, .main-content, .footer {
                padding: 32px 24px;
            }

            .greeting {
                font-size: 28px;
            }

            .congratulations {
                font-size: 20px;
            }

            .logo-text {
                font-size: 24px;
            }

            .logo-image {
                width: 40px;
                height: 40px;
            }

            .contact-info {
                flex-direction: column;
                gap: 12px;
            }

            .welcome-banner, .next-steps, .support-section {
                padding: 32px 24px;
            }

            .welcome-banner-title {
                font-size: 24px;
            }

            .next-steps-title, .support-title {
                font-size: 20px;
            }

            .step-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="email-container">
            <div class="header">
                <div class="logo-container">
                    <div class="logo-image">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 6C8 6, 16 4, 22 8C24 10, 23.5 14, 21 15C18.5 16, 15 14, 14 12C13 10, 8 6, 8 6Z" fill="white" opacity="0.9"/>
                            <path d="M7 16C7 16, 12 18, 17 17C20 16, 21 20, 19 21C17 22, 13 20, 12 18C11 16, 7 16, 7 16Z" fill="white" opacity="0.7"/>
                        </svg>
                    </div>
                    <div class="logo-text">flasti</div>
                </div>
            </div>

            <div class="main-content">
                <div class="greeting">Hola ${fullName},</div>

                <div class="congratulations">¡Felicitaciones! Tu acceso a Flasti ha sido exitoso, y estamos muy emocionados de que formes parte de nuestra comunidad.</div>

                <div class="main-message">
                    Estás a punto de comenzar un viaje increíble, donde aprenderás como aprovechar todo el poder de internet para generar ingresos y llevar tu vida al siguiente nivel.
                </div>

                <div class="access-section">
                    <div class="access-title">Accede a tu cuenta ⬇︎</div>
                </div>

                <div class="welcome-banner">
                    <div class="welcome-banner-content">
                        <div class="welcome-banner-title">¡Bienvenido a Flasti, tu futuro comienza ahora!</div>
                        <div class="welcome-banner-text">
                            Para que disfrutes de todo lo que hemos preparado para ti, simplemente regístrate e inicia sesión pinchando en el botón de aquí abajo.
                        </div>
                        <a href="https://flasti.com/secure-registration-portal-7f9a2b3c5d8e" class="cta-button">
                            REGISTRARME AHORA
                        </a>
                    </div>
                </div>

                <div class="next-steps">
                    <div class="next-steps-title">¿Y ahora qué sigue?</div>

                    <div class="step-content">
                        <div class="step-title">Ingresa a tu panel personal</div>
                        <div class="step-description">
                            Con tu acceso completo, podrás disfrutar de una plataforma diseñada para que aprendas a generar ingresos de forma fácil y segura. Comienza a explorar todas las oportunidades que Flasti tiene para ofrecerte.
                        </div>
                    </div>
                </div>

                <div class="support-section">
                    <div class="support-content">
                        <div class="support-title">Soporte 24/7</div>
                        <div class="support-subtitle">Tu éxito es nuestra misión:<br>¡Aquí estamos para apoyarte!</div>
                        <div class="support-text">
                            Recuerda que en Flasti, estamos aquí para apoyarte en cada paso de este emocionante proceso. Si en algún momento tienes alguna duda o necesitas ayuda, nuestro equipo de soporte está disponible para ayudarte.
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer">
                <div class="contact-info">
                    <a href="mailto:<EMAIL>" class="contact-item"><EMAIL></a>
                    <a href="https://flasti.com" class="contact-item">flasti.com</a>
                </div>

                <p class="copyright">
                    © 2024 Flasti. Todos los derechos reservados.<br>
                    <small>Este email fue enviado porque completaste exitosamente tu registro en Flasti.</small>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
  `;
}
