interface WelcomeEmailData {
  fullName: string;
  email: string;
  transactionId?: string;
  amount?: string;
}

export function getWelcomeEmailTemplate(data: WelcomeEmailData): string {
  const { fullName } = data;

  return `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bienvenido a Flasti</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #334155 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #6B46C1 0%, #3B82F6 50%, #1E3A8A 100%);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .email-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(107, 70, 193, 0.9) 0%, rgba(59, 130, 246, 0.8) 50%, rgba(30, 58, 138, 0.9) 100%);
            z-index: 1;
        }

        .content-wrapper {
            position: relative;
            z-index: 2;
        }

        .header {
            padding: 50px 40px 40px;
            text-align: center;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .logo-svg {
            width: 80px;
            height: 80px;
            margin-right: 16px;
            filter: drop-shadow(0 8px 32px rgba(236, 72, 153, 0.3));
        }

        .logo-text {
            font-size: 36px;
            font-weight: 800;
            color: #FFFFFF;
            letter-spacing: -0.02em;
        }

        .main-content {
            padding: 50px 40px;
            background: rgba(255, 255, 255, 0.02);
        }

        .greeting {
            font-size: 28px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 32px;
            line-height: 1.3;
        }

        .congratulations {
            font-size: 20px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 24px;
            line-height: 1.4;
        }

        .main-message {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 32px;
        }

        .access-section {
            text-align: center;
            margin: 40px 0;
        }

        .access-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 8px;
        }

        .access-subtitle {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 32px;
        }

        .welcome-banner {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 32px;
            margin: 32px 0;
            text-align: center;
        }

        .welcome-banner-title {
            font-size: 22px;
            font-weight: 700;
            color: #FFFFFF;
            margin-bottom: 16px;
        }

        .welcome-banner-text {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 24px;
        }

        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #EC4899 0%, #F97316 100%);
            color: #FFFFFF;
            text-decoration: none;
            padding: 18px 36px;
            border-radius: 16px;
            font-weight: 600;
            font-size: 16px;
            letter-spacing: 0.5px;
            box-shadow: 0 12px 24px rgba(236, 72, 153, 0.4);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .next-steps {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 32px;
            margin: 40px 0;
        }

        .next-steps-title {
            font-size: 20px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 20px;
        }

        .step-content {
            margin-bottom: 24px;
        }

        .step-title {
            font-size: 18px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 12px;
        }

        .step-description {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
        }

        .support-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 32px;
            margin: 40px 0;
            text-align: center;
        }

        .support-title {
            font-size: 20px;
            font-weight: 600;
            color: #FFFFFF;
            margin-bottom: 16px;
        }

        .support-subtitle {
            font-size: 16px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 16px;
        }

        .support-text {
            font-size: 16px;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
        }

        .footer {
            background: rgba(0, 0, 0, 0.2);
            padding: 40px;
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 32px;
            margin: 24px 0;
        }

        .contact-item {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }

        .copyright {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
            margin-top: 24px;
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            body {
                padding: 10px;
            }

            .header, .main-content, .footer {
                padding: 30px 24px;
            }

            .greeting {
                font-size: 24px;
            }

            .congratulations {
                font-size: 18px;
            }

            .logo-text {
                font-size: 28px;
            }

            .logo-svg {
                width: 60px;
                height: 60px;
            }

            .contact-info {
                flex-direction: column;
                gap: 16px;
            }

            .welcome-banner, .next-steps, .support-section {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="content-wrapper">
            <div class="header">
                <div class="logo-container">
                    <svg class="logo-svg" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                        <defs>
                            <linearGradient id="flasti-gradient-1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#EC4899;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#F97316;stop-opacity:1" />
                            </linearGradient>
                            <linearGradient id="flasti-gradient-2" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
                                <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
                            </linearGradient>
                        </defs>
                        <!-- Top shape (pink/orange gradient) -->
                        <path d="M50 40 C50 40, 120 20, 160 60 C180 80, 170 120, 140 130 C110 140, 80 120, 70 90 C60 70, 50 40, 50 40 Z" fill="url(#flasti-gradient-1)"/>
                        <!-- Bottom shape (blue gradient) -->
                        <path d="M40 110 C40 110, 80 140, 120 130 C150 120, 160 160, 130 170 C100 180, 60 160, 50 140 C40 120, 40 110, 40 110 Z" fill="url(#flasti-gradient-2)"/>
                    </svg>
                    <div class="logo-text">flasti</div>
                </div>
            </div>

            <div class="main-content">
                <div class="greeting">Hola ${fullName},</div>

                <div class="congratulations">¡Felicitaciones! Tu acceso a Flasti ha sido exitoso, y estamos muy emocionados de que formes parte de nuestra comunidad.</div>

                <div class="main-message">
                    Estás a punto de comenzar un viaje increíble, donde aprenderás como aprovechar todo el poder de internet para generar ingresos y llevar tu vida al siguiente nivel.
                </div>

                <div class="access-section">
                    <div class="access-title">Accede a tu cuenta ⬇︎</div>
                </div>

                <div class="welcome-banner">
                    <div class="welcome-banner-title">¡Bienvenido a Flasti, tu futuro comienza ahora!</div>
                    <div class="welcome-banner-text">
                        Para que disfrutes de todo lo que hemos preparado para ti, simplemente regístrate e inicia sesión pinchando en el botón de aquí abajo.
                    </div>
                    <a href="https://flasti.com/secure-registration-portal-7f9a2b3c5d8e" class="cta-button">
                        REGISTRARME AHORA
                    </a>
                </div>

                <div class="next-steps">
                    <div class="next-steps-title">¿Y ahora qué sigue?</div>

                    <div class="step-content">
                        <div class="step-title">Ingresa a tu panel personal</div>
                        <div class="step-description">
                            Con tu acceso completo, podrás disfrutar de una plataforma diseñada para que aprendas a generar ingresos de forma fácil y segura. Comienza a explorar todas las oportunidades que Flasti tiene para ofrecerte.
                        </div>
                    </div>
                </div>

                <div class="support-section">
                    <div class="support-title">Soporte 24/7</div>
                    <div class="support-subtitle">Tu éxito es nuestra misión:<br>¡Aquí estamos para apoyarte!</div>
                    <div class="support-text">
                        Recuerda que en Flasti, estamos aquí para apoyarte en cada paso de este emocionante proceso. Si en algún momento tienes alguna duda o necesitas ayuda, nuestro equipo de soporte está disponible para ayudarte.
                    </div>
                </div>
            </div>

            <div class="footer">
                <div class="contact-info">
                    <a href="mailto:<EMAIL>" class="contact-item">📧 <EMAIL></a>
                    <a href="https://flasti.com" class="contact-item">🌐 flasti.com</a>
                </div>

                <p class="copyright">
                    © 2024 Flasti. Todos los derechos reservados.<br>
                    <small>Este email fue enviado porque completaste exitosamente tu registro en Flasti.</small>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
  `;
}
